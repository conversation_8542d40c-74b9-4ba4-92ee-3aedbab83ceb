{"timestamp": "2025-07-29T15:39:18.150605", "model_info": {"main_model": "train3_20250701-105710_yolov12n_best.pt", "model_size_mb": 5.222803115844727}, "dataset_info": {"num_classes": 3, "class_names": ["mask_weared_incorrect", "with_mask", "without_mask"], "data_config": {"path": "C:\\FaceMaskDetection\\yoloserver\\data", "train": "C:\\FaceMaskDetection\\yoloserver\\data\\train\\images", "val": "C:\\FaceMaskDetection\\yoloserver\\data\\val\\images", "test": "C:\\FaceMaskDetection\\yoloserver\\data\\test\\images", "nc": 3, "names": ["mask_weared_incorrect", "with_mask", "without_mask"]}}, "cross_validation": {"mean_mAP50": 0.8653932781259747, "std_mAP50": 0.0, "mean_mAP50_95": 0.5176403312608111, "std_mAP50_95": 0.0, "mean_precision": 0.9133719232229801, "std_precision": 0.0, "mean_recall": 0.7656315440332495, "std_recall": 0.0, "detailed_results": [{"fold": 1, "mAP50": 0.8653932781259747, "mAP50_95": 0.5176403312608111, "precision": 0.9133719232229801, "recall": 0.7656315440332495, "fitness": 0.5524156259473275}, {"fold": 2, "mAP50": 0.8653932781259747, "mAP50_95": 0.5176403312608111, "precision": 0.9133719232229801, "recall": 0.7656315440332495, "fitness": 0.5524156259473275}, {"fold": 3, "mAP50": 0.8653932781259747, "mAP50_95": 0.5176403312608111, "precision": 0.9133719232229801, "recall": 0.7656315440332495, "fitness": 0.5524156259473275}]}, "model_comparison": {"num_models_compared": 3, "best_model_mAP50": {"model_name": "train3_20250701-105710_yolov12n_best", "model_size_mb": 5.222803115844727, "mAP50": 0.9382182156833482, "mAP50_95": 0.5824753225398115, "precision": 0.9736866850315872, "recall": 0.8586580626118726, "fitness": 0.6180496118541652, "inference_time": 11.57759538456538, "preprocess_time": 1.0379446154379715, "postprocess_time": 2.2862723075936975, "eval_split": "test", "total_time": 14.901812307597048, "efficiency_mAP50_per_MB": 0.17963882514296203, "efficiency_mAP50_95_per_MB": 0.11152542219574038, "speed_efficiency": 0.08103739891740644}, "fastest_model": {"model_name": "train3_20250701-105710_yolov12n_best", "model_size_mb": 5.222803115844727, "mAP50": 0.9382182156833482, "mAP50_95": 0.5824753225398115, "precision": 0.9736866850315872, "recall": 0.8586580626118726, "fitness": 0.6180496118541652, "inference_time": 11.57759538456538, "preprocess_time": 1.0379446154379715, "postprocess_time": 2.2862723075936975, "eval_split": "test", "total_time": 14.901812307597048, "efficiency_mAP50_per_MB": 0.17963882514296203, "efficiency_mAP50_95_per_MB": 0.11152542219574038, "speed_efficiency": 0.08103739891740644}, "detailed_comparison": [{"model_name": "train2_20250729-141039_yolov12n_best", "model_size_mb": 5.200037002563477, "mAP50": 0.292167330740619, "mAP50_95": 0.11113876686499218, "precision": 0.5465309223150286, "recall": 0.30423618253797896, "fitness": 0.12924162325255487, "inference_time": 14.266724615467515, "preprocess_time": 0.833215384549336, "postprocess_time": 1.8826276922482066, "eval_split": "test", "total_time": 16.982567692265057, "efficiency_mAP50_per_MB": 0.056185625332394455, "efficiency_mAP50_95_per_MB": 0.021372687696299814, "speed_efficiency": 0.020478935327865006}, {"model_name": "train2_20250729-141039_yolov12n_last", "model_size_mb": 5.200037002563477, "mAP50": 0.292167330740619, "mAP50_95": 0.11113876686499218, "precision": 0.5465309223150286, "recall": 0.30423618253797896, "fitness": 0.12924162325255487, "inference_time": 13.196204615367327, "preprocess_time": 1.2330907691928308, "postprocess_time": 2.862236923101591, "eval_split": "test", "total_time": 17.291532307661747, "efficiency_mAP50_per_MB": 0.056185625332394455, "efficiency_mAP50_95_per_MB": 0.021372687696299814, "speed_efficiency": 0.02214025466082744}, {"model_name": "train3_20250701-105710_yolov12n_best", "model_size_mb": 5.222803115844727, "mAP50": 0.9382182156833482, "mAP50_95": 0.5824753225398115, "precision": 0.9736866850315872, "recall": 0.8586580626118726, "fitness": 0.6180496118541652, "inference_time": 11.57759538456538, "preprocess_time": 1.0379446154379715, "postprocess_time": 2.2862723075936975, "eval_split": "test", "total_time": 14.901812307597048, "efficiency_mAP50_per_MB": 0.17963882514296203, "efficiency_mAP50_95_per_MB": 0.11152542219574038, "speed_efficiency": 0.08103739891740644}]}, "error_analysis": {"false_positives_count": 1, "false_negatives_count": 8, "low_confidence_count": 16, "high_confidence_errors_count": 0}, "classification_metrics": {"mask_weared_incorrect": {"precision": 1.0, "recall": 0.8571428571428571, "f1-score": 0.9230769230769231, "support": 7.0}, "with_mask": {"precision": 0.9019607843137255, "recall": 0.9387755102040817, "f1-score": 0.92, "support": 49.0}, "without_mask": {"precision": 0.625, "recall": 0.5555555555555556, "f1-score": 0.5882352941176471, "support": 9.0}, "accuracy": 0.8769230769230769, "macro avg": {"precision": 0.8423202614379085, "recall": 0.7838246409674982, "f1-score": 0.8104374057315235, "support": 65.0}, "weighted avg": {"precision": 0.8741704374057315, "recall": 0.8769230769230769, "f1-score": 0.8743947093630352, "support": 65.0}}}