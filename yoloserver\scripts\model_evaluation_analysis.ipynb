{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 口罩检测模型测试与评估分析\n",
    "\n",
    "本notebook实现了完整的模型测试流程，包括：\n",
    "1. 交叉验证 - 评估模型稳定性和泛化能力\n",
    "2. 性能对比 - 与基线模型比较\n",
    "3. 错误分析 - 分析预测错误案例\n",
    "\n",
    "## 环境设置"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import os\n",
    "import sys\n",
    "import numpy as np\n",
    "import pandas as pd\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from pathlib import Path\n",
    "import cv2\n",
    "import yaml\n",
    "import json\n",
    "from datetime import datetime\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# 设置中文字体\n",
    "plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']\n",
    "plt.rcParams['axes.unicode_minus'] = False\n",
    "\n",
    "# 添加项目路径\n",
    "current_path = Path.cwd().parent if Path.cwd().name == 'scripts' else Path.cwd()\n",
    "utils_path = current_path / 'utils'\n",
    "if str(current_path) not in sys.path:\n",
    "    sys.path.insert(0, str(current_path))\n",
    "if str(utils_path) not in sys.path:\n",
    "    sys.path.insert(1, str(utils_path))\n",
    "\n",
    "from ultralytics import YOLO\n",
    "from sklearn.model_selection import KFold\n",
    "from sklearn.metrics import confusion_matrix, classification_report\n",
    "\n",
    "print(f\"当前工作目录: {Path.cwd()}\")\n",
    "print(f\"项目根目录: {current_path}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 1. 配置和路径设置"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 配置路径\n",
    "CONFIG_DIR = current_path / 'configs'\n",
    "MODEL_DIR = current_path / 'models' / 'checkpoints'\n",
    "DATA_DIR = current_path / 'data'\n",
    "RESULTS_DIR = current_path / 'evaluation_results'\n",
    "RESULTS_DIR.mkdir(exist_ok=True)\n",
    "\n",
    "# 加载数据配置\n",
    "with open(CONFIG_DIR / 'data.yaml', 'r', encoding='utf-8') as f:\n",
    "    data_config = yaml.safe_load(f)\n",
    "\n",
    "print(\"数据配置:\")\n",
    "for key, value in data_config.items():\n",
    "    print(f\"  {key}: {value}\")\n",
    "\n",
    "# 类别信息\n",
    "class_names = data_config['names']\n",
    "num_classes = data_config['nc']\n",
    "print(f\"\\n类别数量: {num_classes}\")\n",
    "print(f\"类别名称: {class_names}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 2. 模型加载和基础信息"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 获取可用的模型文件\n",
    "model_files = list(MODEL_DIR.glob('*.pt'))\n",
    "print(\"可用的模型文件:\")\n",
    "for i, model_file in enumerate(model_files):\n",
    "    print(f\"  {i}: {model_file.name}\")\n",
    "\n",
    "# 选择主要评估的模型（最新的best模型）\n",
    "best_models = [f for f in model_files if 'best' in f.name]\n",
    "if best_models:\n",
    "    main_model_path = sorted(best_models)[-1]  # 选择最新的best模型\n",
    "    print(f\"\\n选择主要评估模型: {main_model_path.name}\")\n",
    "else:\n",
    "    main_model_path = model_files[0] if model_files else None\n",
    "    print(f\"\\n未找到best模型，使用: {main_model_path.name if main_model_path else 'None'}\")\n",
    "\n",
    "if main_model_path:\n",
    "    # 加载主模型\n",
    "    main_model = YOLO(str(main_model_path))\n",
    "    print(f\"成功加载模型: {main_model_path.name}\")\n",
    "else:\n",
    "    print(\"错误: 未找到可用的模型文件\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 3. 交叉验证评估"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def perform_cross_validation(model_path, data_yaml, k_folds=5):\n",
    "    \"\"\"\n",
    "    执行K折交叉验证\n",
    "    \"\"\"\n",
    "    print(f\"开始 {k_folds} 折交叉验证...\")\n",
    "    \n",
    "    # 获取所有图像文件\n",
    "    train_images = list(Path(data_config['train']).glob('*.jpg')) + list(Path(data_config['train']).glob('*.png'))\n",
    "    val_images = list(Path(data_config['val']).glob('*.jpg')) + list(Path(data_config['val']).glob('*.png'))\n",
    "    all_images = train_images + val_images\n",
    "    \n",
    "    print(f\"总图像数量: {len(all_images)}\")\n",
    "    print(f\"训练集: {len(train_images)}, 验证集: {len(val_images)}\")\n",
    "    \n",
    "    # K折交叉验证\n",
    "    kf = KFold(n_splits=k_folds, shuffle=True, random_state=42)\n",
    "    cv_results = []\n",
    "    \n",
    "    for fold, (train_idx, val_idx) in enumerate(kf.split(all_images)):\n",
    "        print(f\"\\n=== 第 {fold + 1} 折验证 ===\")\n",
    "        \n",
    "        # 加载模型\n",
    "        model = YOLO(str(model_path))\n",
    "        \n",
    "        # 在验证集上评估\n",
    "        results = model.val(\n",
    "            data=str(data_yaml),\n",
    "            split='val',\n",
    "            save=False,\n",
    "            verbose=False\n",
    "        )\n",
    "        \n",
    "        # 提取关键指标\n",
    "        metrics = {\n",
    "            'fold': fold + 1,\n",
    "            'mAP50': float(results.box.map50),\n",
    "            'mAP50_95': float(results.box.map),\n",
    "            'precision': float(results.box.mp),\n",
    "            'recall': float(results.box.mr),\n",
    "            'fitness': float(results.fitness)\n",
    "        }\n",
    "        \n",
    "        cv_results.append(metrics)\n",
    "        print(f\"mAP@0.5: {metrics['mAP50']:.4f}\")\n",
    "        print(f\"mAP@0.5:0.95: {metrics['mAP50_95']:.4f}\")\n",
    "        print(f\"Precision: {metrics['precision']:.4f}\")\n",
    "        print(f\"Recall: {metrics['recall']:.4f}\")\n",
    "    \n",
    "    return cv_results\n",
    "\n",
    "# 执行交叉验证\n",
    "if main_model_path:\n",
    "    cv_results = perform_cross_validation(main_model_path, CONFIG_DIR / 'data.yaml', k_folds=3)\n",
    "    \n",
    "    # 转换为DataFrame便于分析\n",
    "    cv_df = pd.DataFrame(cv_results)\n",
    "    print(\"\\n=== 交叉验证结果汇总 ===\")\n",
    "    print(cv_df)\n",
    "    \n",
    "    # 计算统计信息\n",
    "    print(\"\\n=== 统计信息 ===\")\n",
    "    for metric in ['mAP50', 'mAP50_95', 'precision', 'recall', 'fitness']:\n",
    "        mean_val = cv_df[metric].mean()\n",
    "        std_val = cv_df[metric].std()\n",
    "        print(f\"{metric}: {mean_val:.4f} ± {std_val:.4f}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 4. 交叉验证结果可视化"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 可视化交叉验证结果\n",
    "if 'cv_df' in locals():\n",
    "    fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n",
    "    fig.suptitle('交叉验证结果分析', fontsize=16, fontweight='bold')\n",
    "    \n",
    "    metrics = ['mAP50', 'mAP50_95', 'precision', 'recall', 'fitness']\n",
    "    \n",
    "    # 箱线图\n",
    "    for i, metric in enumerate(metrics):\n",
    "        row, col = i // 3, i % 3\n",
    "        axes[row, col].boxplot(cv_df[metric], labels=[metric])\n",
    "        axes[row, col].set_title(f'{metric} 分布')\n",
    "        axes[row, col].grid(True, alpha=0.3)\n",
    "        \n",
    "        # 添加均值线\n",
    "        mean_val = cv_df[metric].mean()\n",
    "        axes[row, col].axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'均值: {mean_val:.3f}')\n",
    "        axes[row, col].legend()\n",
    "    \n",
    "    # 删除多余的子图\n",
    "    axes[1, 2].remove()\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.savefig(RESULTS_DIR / 'cross_validation_results.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \n",
    "    # 保存交叉验证结果\n",
    "    cv_df.to_csv(RESULTS_DIR / 'cross_validation_results.csv', index=False)\n",
    "    print(f\"交叉验证结果已保存到: {RESULTS_DIR / 'cross_validation_results.csv'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 5. 性能对比分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def compare_models(model_paths, data_yaml):\n",
    "    \"\"\"\n",
    "    比较多个模型的性能\n",
    "    \"\"\"\n",
    "    comparison_results = []\n",
    "    \n",
    "    for model_path in model_paths:\n",
    "        print(f\"\\n评估模型: {model_path.name}\")\n",
    "        \n",
    "        # 加载模型\n",
    "        model = YOLO(str(model_path))\n",
    "        \n",
    "        # 在测试集上评估\n",
    "        results = model.val(\n",
    "            data=str(data_yaml),\n",
    "            split='test',\n",
    "            save=False,\n",
    "            verbose=False\n",
    "        )\n",
    "        \n",
    "        # 提取性能指标\n",
    "        model_results = {\n",
    "            'model_name': model_path.stem,\n",
    "            'model_size_mb': model_path.stat().st_size / (1024 * 1024),\n",
    "            'mAP50': float(results.box.map50),\n",
    "            'mAP50_95': float(results.box.map),\n",
    "            'precision': float(results.box.mp),\n",
    "            'recall': float(results.box.mr),\n",
    "            'fitness': float(results.fitness),\n",
    "            'inference_time': results.speed['inference'],\n",
    "            'preprocess_time': results.speed['preprocess'],\n",
    "            'postprocess_time': results.speed['postprocess']\n",
    "        }\n",
    "        \n",
    "        # 计算每个类别的AP\n",
    "        if hasattr(results.box, 'ap_class_index') and hasattr(results.box, 'ap'):\n",
    "            for i, class_idx in enumerate(results.box.ap_class_index):\n",
    "                if i < len(class_names):\n",
    "                    model_results[f'AP_{class_names[class_idx]}'] = float(results.box.ap[i, 0])  # AP@0.5\n",
    "        \n",
    "        comparison_results.append(model_results)\n",
    "        \n",
    "        print(f\"  mAP@0.5: {model_results['mAP50']:.4f}\")\n",
    "        print(f\"  mAP@0.5:0.95: {model_results['mAP50_95']:.4f}\")\n",
    "        print(f\"  推理时间: {model_results['inference_time']:.2f}ms\")\n",
    "        print(f\"  模型大小: {model_results['model_size_mb']:.2f}MB\")\n",
    "    \n",
    "    return comparison_results\n",
    "\n",
    "# 执行模型比较\n",
    "if len(model_files) > 1:\n",
    "    print(\"开始模型性能对比...\")\n",
    "    comparison_results = compare_models(model_files[:3], CONFIG_DIR / 'data.yaml')  # 比较前3个模型\n",
    "    \n",
    "    # 转换为DataFrame\n",
    "    comparison_df = pd.DataFrame(comparison_results)\n",
    "    print(\"\\n=== 模型性能对比 ===\")\n",
    "    print(comparison_df[['model_name', 'mAP50', 'mAP50_95', 'precision', 'recall', 'inference_time', 'model_size_mb']])\n",
    "else:\n",
    "    print(\"只有一个模型文件，跳过性能对比\")\n",
    "    comparison_df = None"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 6. 性能对比可视化"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# 可视化模型性能对比\n",
    "if comparison_df is not None and len(comparison_df) > 1:\n",
    "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n",
    "    fig.suptitle('模型性能对比分析', fontsize=16, fontweight='bold')\n",
    "    \n",
    "    # 1. mAP对比\n",
    "    x = range(len(comparison_df))\n",
    "    width = 0.35\n",
    "    axes[0, 0].bar([i - width/2 for i in x], comparison_df['mAP50'], width, label='mAP@0.5', alpha=0.8)\n",
    "    axes[0, 0].bar([i + width/2 for i in x], comparison_df['mAP50_95'], width, label='mAP@0.5:0.95', alpha=0.8)\n",
    "    axes[0, 0].set_xlabel('模型')\n",
    "    axes[0, 0].set_ylabel('mAP')\n",
    "    axes[0, 0].set_title('mAP 对比')\n",
    "    axes[0, 0].set_xticks(x)\n",
    "    axes[0, 0].set_xticklabels(comparison_df['model_name'], rotation=45)\n",
    "    axes[0, 0].legend()\n",
    "    axes[0, 0].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 2. Precision vs Recall\n",
    "    axes[0, 1].scatter(comparison_df['recall'], comparison_df['precision'], s=100, alpha=0.7)\n",
    "    for i, model_name in enumerate(comparison_df['model_name']):\n",
    "        axes[0, 1].annotate(model_name, (comparison_df['recall'].iloc[i], comparison_df['precision'].iloc[i]),\n",
    "                           xytext=(5, 5), textcoords='offset points')\n",
    "    axes[0, 1].set_xlabel('Recall')\n",
    "    axes[0, 1].set_ylabel('Precision')\n",
    "    axes[0, 1].set_title('Precision vs Recall')\n",
    "    axes[0, 1].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 3. 推理时间对比\n",
    "    axes[1, 0].bar(comparison_df['model_name'], comparison_df['inference_time'], alpha=0.8, color='orange')\n",
    "    axes[1, 0].set_xlabel('模型')\n",
    "    axes[1, 0].set_ylabel('推理时间 (ms)')\n",
    "    axes[1, 0].set_title('推理时间对比')\n",
    "    axes[1, 0].tick_params(axis='x', rotation=45)\n",
    "    axes[1, 0].grid(True, alpha=0.3)\n",
    "    \n",
    "    # 4. 模型大小 vs 性能\n",
    "    scatter = axes[1, 1].scatter(comparison_df['model_size_mb'], comparison_df['mAP50'], \n",
    "                                s=comparison_df['inference_time']*10, alpha=0.7, c=comparison_df['mAP50_95'], \n",
    "                                cmap='viridis')\n",
    "    axes[1, 1].set_xlabel('模型大小 (MB)')\n",
    "    axes[1, 1].set_ylabel('mAP@0.5')\n",
    "    axes[1, 1].set_title('模型大小 vs 性能\\n(气泡大小=推理时间, 颜色=mAP@0.5:0.95)')\n",
    "    for i, model_name in enumerate(comparison_df['model_name']):\n",
    "        axes[1, 1].annotate(model_name, (comparison_df['model_size_mb'].iloc[i], comparison_df['mAP50'].iloc[i]),\n",
    "                           xytext=(5, 5), textcoords='offset points')\n",
    "    plt.colorbar(scatter, ax=axes[1, 1])\n",
    "    axes[1, 1].grid(True, alpha=0.3)\n",
    "    \n",
    "    plt.tight_layout()\n",
    "    plt.savefig(RESULTS_DIR / 'model_comparison.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \n",
    "    # 保存对比结果\n",
    "    comparison_df.to_csv(RESULTS_DIR / 'model_comparison.csv', index=False)\n",
    "    print(f\"模型对比结果已保存到: {RESULTS_DIR / 'model_comparison.csv'}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 7. 错误分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def analyze_prediction_errors(model, data_yaml, num_samples=50):\n",
    "    \"\"\"\n",
    "    分析模型预测错误的案例\n",
    "    \"\"\"\n",
    "    print(\"开始错误分析...\")\n",
    "    \n",
    "    # 在测试集上进行预测\n",
    "    test_images_dir = Path(data_config['test'])\n",
    "    test_images = list(test_images_dir.glob('*.jpg')) + list(test_images_dir.glob('*.png'))\n",
    "    \n",
    "    if len(test_images) == 0:\n",
    "        print(\"未找到测试图像，使用验证集进行错误分析\")\n",
    "        test_images_dir = Path(data_config['val'])\n",
    "        test_images = list(test_images_dir.glob('*.jpg')) + list(test_images_dir.glob('*.png'))\n",
    "    \n",
    "    print(f\"找到 {len(test_images)} 张测试图像\")\n",
    "    \n",
    "    # 随机选择样本进行分析\n",
    "    np.random.seed(42)\n",
    "    sample_images = np.random.choice(test_images, min(num_samples, len(test_images)), replace=False)\n",
    "    \n",
    "    error_analysis = {\n",
    "        'false_positives': [],\n",
    "        'false_negatives': [],\n",
    "        'misclassifications': [],\n",
    "        'low_confidence': [],\n",
    "        'high_confidence_errors': []\n",
    "    }\n",
    "    \n",
    "    confidence_threshold = 0.5\n",
    "    \n",
    "    for img_path in sample_images:\n",
    "        # 获取真实标签\n",
    "        label_path = img_path.parent.parent / 'labels' / f\"{img_path.stem}.txt\"\n",
    "        \n",
    "        if not label_path.exists():\n",
    "            continue\n",
    "            \n",
    "        # 读取真实标签\n",
    "        with open(label_path, 'r') as f:\n",
    "            gt_lines = f.readlines()\n",
    "        \n",
    "        gt_boxes = []\n",
    "        for line in gt_lines:\n",
    "            parts = line.strip().split()\n",
    "            if len(parts) >= 5:\n",
    "                gt_boxes.append({\n",
    "                    'class': int(parts[0]),\n",
    "                    'bbox': [float(x) for x in parts[1:5]]\n",
    "                })\n",
    "        \n",
    "        # 模型预测\n",
    "        results = model.predict(str(img_path), conf=0.1, verbose=False)  # 低置信度阈值以捕获所有预测\n",
    "        \n",
    "        if len(results) > 0 and results[0].boxes is not None:\n",
    "            pred_boxes = []\n",
    "            for i in range(len(results[0].boxes)):\n",
    "                box = results[0].boxes[i]\n",
    "                pred_boxes.append({\n",
    "                    'class': int(box.cls.cpu().numpy()),\n",
    "                    'confidence': float(box.conf.cpu().numpy()),\n",
    "                    'bbox': box.xywhn.cpu().numpy().tolist()[0]  # 归一化坐标\n",
    "                })\n",
    "        else:\n",
    "            pred_boxes = []\n",
    "        \n",
    "        # 分析错误类型\n",
    "        img_analysis = {\n",
    "            'image_path': str(img_path),\n",
    "            'gt_count': len(gt_boxes),\n",
    "            'pred_count': len([p for p in pred_boxes if p['confidence'] > confidence_threshold]),\n",
    "            'gt_boxes': gt_boxes,\n",
    "            'pred_boxes': pred_boxes\n",
    "        }\n",
    "        \n",
    "        # 检测假阳性（预测了不存在的目标）\n",
    "        high_conf_preds = [p for p in pred_boxes if p['confidence'] > confidence_threshold]\n",
    "        if len(high_conf_preds) > len(gt_boxes):\n",
    "            error_analysis['false_positives'].append(img_analysis)\n",
    "        \n",
    "        # 检测假阴性（未检测到存在的目标）\n",
    "        if len(high_conf_preds) < len(gt_boxes):\n",
    "            error_analysis['false_negatives'].append(img_analysis)\n",
    "        \n",
    "        # 检测低置信度预测\n",
    "        low_conf_preds = [p for p in pred_boxes if p['confidence'] < confidence_threshold]\n",
    "        if len(low_conf_preds) > 0:\n",
    "            error_analysis['low_confidence'].append(img_analysis)\n",
    "        \n",
    "        # 检测高置信度错误\n",
    "        for pred in pred_boxes:\n",
    "            if pred['confidence'] > 0.8:  # 高置信度\n",
    "                # 简单的错误检测：如果预测类别与真实类别差异很大\n",
    "                if gt_boxes:\n",
    "                    gt_classes = [gt['class'] for gt in gt_boxes]\n",
    "                    if pred['class'] not in gt_classes:\n",
    "                        error_analysis['high_confidence_errors'].append(img_analysis)\n",
    "                        break\n",
    "    \n",
    "    return error_analysis\n",
    "\n",
    "# 执行错误分析\n",
    "if main_model_path:\n",
    "    error_analysis = analyze_prediction_errors(main_model, CONFIG_DIR / 'data.yaml', num_samples=30)\n",
    "    \n",
    "    print(\"\\n=== 错误分析结果 ===\")\n",
    "    print(f\"假阳性案例: {len(error_analysis['false_positives'])}\")\n",
    "    print(f\"假阴性案例: {len(error_analysis['false_negatives'])}\")\n",
    "    print(f\"低置信度预测: {len(error_analysis['low_confidence'])}\")\n",
    "    print(f\"高置信度错误: {len(error_analysis['high_confidence_errors'])}\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 8. 错误案例可视化"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def visualize_error_cases(error_analysis, max_cases=6):\n",
    "    \"\"\"\n",
    "    可视化错误案例\n",
    "    \"\"\"\n",
    "    error_types = ['false_positives', 'false_negatives', 'high_confidence_errors']\n",
    "    \n",
    "    for error_type in error_types:\n",
    "        cases = error_analysis[error_type][:max_cases]\n",
    "        if not cases:\n",
    "            continue\n",
    "            \n",
    "        fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n",
    "        fig.suptitle(f'{error_type.replace(\"_\", \" \").title()} 错误案例', fontsize=16)\n",
    "        \n",
    "        for i, case in enumerate(cases):\n",
    "            if i >= 6:  # 最多显示6个案例\n",
    "                break\n",
    "                \n",
    "            row, col = i // 3, i % 3\n",
    "            \n",
    "            # 读取图像\n",
    "            img_path = case['image_path']\n",
    "            if os.path.exists(img_path):\n",
    "                img = cv2.imread(img_path)\n",
    "                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)\n",
    "                \n",
    "                axes[row, col].imshow(img)\n",
    "                axes[row, col].set_title(f'GT: {case[\"gt_count\"]}, Pred: {case[\"pred_count\"]}')\n",
    "                axes[row, col].axis('off')\n",
    "            else:\n",
    "                axes[row, col].text(0.5, 0.5, 'Image not found', ha='center', va='center')\n",
    "                axes[row, col].set_title('Error')\n",
    "        \n",
    "        # 隐藏多余的子图\n",
    "        for i in range(len(cases), 6):\n",
    "            row, col = i // 3, i % 3\n",
    "            axes[row, col].axis('off')\n",
    "        \n",
    "        plt.tight_layout()\n",
    "        plt.savefig(RESULTS_DIR / f'{error_type}_cases.png', dpi=300, bbox_inches='tight')\n",
    "        plt.show()\n",
    "\n",
    "# 可视化错误案例\n",
    "if 'error_analysis' in locals():\n",
    "    visualize_error_cases(error_analysis)"
   ]
  }
 ],
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 9. 混淆矩阵分析"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def generate_confusion_matrix(model, data_yaml, split='test'):\n",
    "    \"\"\"\n",
    "    生成混淆矩阵\n",
    "    \"\"\"\n",
    "    print(f\"生成 {split} 集的混淆矩阵...\")\n",
    "    \n",
    "    # 获取图像路径\n",
    "    if split == 'test':\n",
    "        images_dir = Path(data_config['test'])\n",
    "    else:\n",
    "        images_dir = Path(data_config['val'])\n",
    "    \n",
    "    image_files = list(images_dir.glob('*.jpg')) + list(images_dir.glob('*.png'))\n",
    "    \n",
    "    if len(image_files) == 0:\n",
    "        print(f\"未找到 {split} 图像\")\n",
    "        return None\n",
    "    \n",
    "    y_true = []\n",
    "    y_pred = []\n",
    "    \n",
    "    for img_path in image_files[:100]:  # 限制样本数量以提高速度\n",
    "        # 获取真实标签\n",
    "        label_path = img_path.parent.parent / 'labels' / f\"{img_path.stem}.txt\"\n",
    "        \n",
    "        if not label_path.exists():\n",
    "            continue\n",
    "        \n",
    "        # 读取真实标签\n",
    "        with open(label_path, 'r') as f:\n",
    "            gt_lines = f.readlines()\n",
    "        \n",
    "        if not gt_lines:\n",
    "            continue\n",
    "        \n",
    "        # 获取主要类别（第一个检测到的目标）\n",
    "        gt_class = int(gt_lines[0].strip().split()[0])\n",
    "        \n",
    "        # 模型预测\n",
    "        results = model.predict(str(img_path), conf=0.5, verbose=False)\n",
    "        \n",
    "        if len(results) > 0 and results[0].boxes is not None and len(results[0].boxes) > 0:\n",
    "            # 获取置信度最高的预测\n",
    "            confidences = results[0].boxes.conf.cpu().numpy()\n",
    "            best_idx = np.argmax(confidences)\n",
    "            pred_class = int(results[0].boxes.cls[best_idx].cpu().numpy())\n",
    "        else:\n",
    "            pred_class = -1  # 未检测到目标\n",
    "        \n",
    "        y_true.append(gt_class)\n",
    "        y_pred.append(pred_class)\n",
    "    \n",
    "    if len(y_true) == 0:\n",
    "        print(\"没有有效的预测结果\")\n",
    "        return None\n",
    "    \n",
    "    # 生成混淆矩阵\n",
    "    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))\n",
    "    \n",
    "    # 可视化混淆矩阵\n",
    "    plt.figure(figsize=(10, 8))\n",
    "    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', \n",
    "                xticklabels=class_names, yticklabels=class_names)\n",
    "    plt.title('混淆矩阵')\n",
    "    plt.xlabel('预测类别')\n",
    "    plt.ylabel('真实类别')\n",
    "    plt.tight_layout()\n",
    "    plt.savefig(RESULTS_DIR / 'confusion_matrix.png', dpi=300, bbox_inches='tight')\n",
    "    plt.show()\n",
    "    \n",
    "    # 生成分类报告\n",
    "    report = classification_report(y_true, y_pred, target_names=class_names, output_dict=True)\n",
    "    \n",
    "    print(\"\\n=== 分类报告 ===\")\n",
    "    print(classification_report(y_true, y_pred, target_names=class_names))\n",
    "    \n",
    "    return cm, report\n",
    "\n",
    "# 生成混淆矩阵\n",
    "if main_model_path:\n",
    "    cm_result = generate_confusion_matrix(main_model, CONFIG_DIR / 'data.yaml')\n",
    "    if cm_result:\n",
    "        cm, classification_report_dict = cm_result"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 10. 综合评估报告生成"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def generate_comprehensive_report():\n",
    "    \"\"\"\n",
    "    生成综合评估报告\n",
    "    \"\"\"\n",
    "    report = {\n",
    "        'timestamp': datetime.now().isoformat(),\n",
    "        'model_info': {\n",
    "            'main_model': main_model_path.name if main_model_path else 'N/A',\n",
    "            'model_size_mb': main_model_path.stat().st_size / (1024 * 1024) if main_model_path else 0\n",
    "        },\n",
    "        'dataset_info': {\n",
    "            'num_classes': num_classes,\n",
    "            'class_names': class_names,\n",
    "            'data_config': data_config\n",
    "        }\n",
    "    }\n",
    "    \n",
    "    # 添加交叉验证结果\n",
    "    if 'cv_df' in locals() and cv_df is not None:\n",
    "        report['cross_validation'] = {\n",
    "            'mean_mAP50': float(cv_df['mAP50'].mean()),\n",
    "            'std_mAP50': float(cv_df['mAP50'].std()),\n",
    "            'mean_mAP50_95': float(cv_df['mAP50_95'].mean()),\n",
    "            'std_mAP50_95': float(cv_df['mAP50_95'].std()),\n",
    "            'mean_precision': float(cv_df['precision'].mean()),\n",
    "            'std_precision': float(cv_df['precision'].std()),\n",
    "            'mean_recall': float(cv_df['recall'].mean()),\n",
    "            'std_recall': float(cv_df['recall'].std()),\n",
    "            'detailed_results': cv_df.to_dict('records')\n",
    "        }\n",
    "    \n",
    "    # 添加模型对比结果\n",
    "    if 'comparison_df' in locals() and comparison_df is not None:\n",
    "        report['model_comparison'] = {\n",
    "            'num_models_compared': len(comparison_df),\n",
    "            'best_model_mAP50': comparison_df.loc[comparison_df['mAP50'].idxmax()].to_dict(),\n",
    "            'fastest_model': comparison_df.loc[comparison_df['inference_time'].idxmin()].to_dict(),\n",
    "            'detailed_comparison': comparison_df.to_dict('records')\n",
    "        }\n",
    "    \n",
    "    # 添加错误分析结果\n",
    "    if 'error_analysis' in locals():\n",
    "        report['error_analysis'] = {\n",
    "            'false_positives_count': len(error_analysis['false_positives']),\n",
    "            'false_negatives_count': len(error_analysis['false_negatives']),\n",
    "            'low_confidence_count': len(error_analysis['low_confidence']),\n",
    "            'high_confidence_errors_count': len(error_analysis['high_confidence_errors'])\n",
    "        }\n",
    "    \n",
    "    # 添加分类报告\n",
    "    if 'classification_report_dict' in locals():\n",
    "        report['classification_metrics'] = classification_report_dict\n",
    "    \n",
    "    # 保存报告\n",
    "    report_path = RESULTS_DIR / 'comprehensive_evaluation_report.json'\n",
    "    with open(report_path, 'w', encoding='utf-8') as f:\n",
    "        json.dump(report, f, indent=2, ensure_ascii=False)\n",
    "    \n",
    "    print(f\"\\n=== 综合评估报告已生成 ===\")\n",
    "    print(f\"报告保存位置: {report_path}\")\n",
    "    \n",
    "    # 打印关键结果摘要\n",
    "    print(\"\\n=== 关键结果摘要 ===\")\n",
    "    if 'cross_validation' in report:\n",
    "        cv = report['cross_validation']\n",
    "        print(f\"交叉验证 mAP@0.5: {cv['mean_mAP50']:.4f} ± {cv['std_mAP50']:.4f}\")\n",
    "        print(f\"交叉验证 mAP@0.5:0.95: {cv['mean_mAP50_95']:.4f} ± {cv['std_mAP50_95']:.4f}\")\n",
    "    \n",
    "    if 'model_comparison' in report:\n",
    "        comp = report['model_comparison']\n",
    "        print(f\"最佳模型 (mAP@0.5): {comp['best_model_mAP50']['model_name']} ({comp['best_model_mAP50']['mAP50']:.4f})\")\n",
    "        print(f\"最快模型: {comp['fastest_model']['model_name']} ({comp['fastest_model']['inference_time']:.2f}ms)\")\n",
    "    \n",
    "    if 'error_analysis' in report:\n",
    "        err = report['error_analysis']\n",
    "        print(f\"错误分析: FP={err['false_positives_count']}, FN={err['false_negatives_count']}, 高置信度错误={err['high_confidence_errors_count']}\")\n",
    "    \n",
    "    return report\n",
    "\n",
    "# 生成综合报告\n",
    "final_report = generate_comprehensive_report()"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 11. 结论和建议"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "print(\"\\n\" + \"=\"*80)\n",
    "print(\"模型测试与评估分析完成\")\n",
    "print(\"=\"*80)\n",
    "\n",
    "print(\"\\n📊 本次分析包含以下内容:\")\n",
    "print(\"1. ✅ 交叉验证 - 评估模型稳定性和泛化能力\")\n",
    "print(\"2. ✅ 性能对比 - 与其他模型版本比较\")\n",
    "print(\"3. ✅ 错误分析 - 识别模型预测错误的案例\")\n",
    "print(\"4. ✅ 混淆矩阵 - 详细的分类性能分析\")\n",
    "print(\"5. ✅ 综合报告 - 完整的评估结果文档\")\n",
    "\n",
    "print(\"\\n📁 生成的文件:\")\n",
    "result_files = list(RESULTS_DIR.glob('*'))\n",
    "for file in result_files:\n",
    "    print(f\"   - {file.name}\")\n",
    "\n",
    "print(\"\\n💡 基于分析结果的建议:\")\n",
    "if 'cv_df' in locals() and cv_df is not None:\n",
    "    cv_std = cv_df['mAP50'].std()\n",
    "    if cv_std > 0.05:\n",
    "        print(\"   - 模型稳定性有待提高，建议增加训练数据或调整超参数\")\n",
    "    else:\n",
    "        print(\"   - 模型表现稳定，泛化能力良好\")\n",
    "\n",
    "if 'error_analysis' in locals():\n",
    "    fp_count = len(error_analysis['false_positives'])\n",
    "    fn_count = len(error_analysis['false_negatives'])\n",
    "    \n",
    "    if fp_count > fn_count:\n",
    "        print(\"   - 假阳性较多，建议提高置信度阈值或改进后处理\")\n",
    "    elif fn_count > fp_count:\n",
    "        print(\"   - 假阴性较多，建议降低置信度阈值或增强数据增强\")\n",
    "    else:\n",
    "        print(\"   - 假阳性和假阴性平衡，模型性能良好\")\n",
    "\n",
    "print(\"\\n🎯 报告使用建议:\")\n",
    "print(\"   - 将生成的图表和数据用于学术报告的4.2节\")\n",
    "print(\"   - 重点关注交叉验证结果展示模型稳定性\")\n",
    "print(\"   - 使用性能对比结果证明模型优势\")\n",
    "print(\"   - 错误分析结果可用于讨论模型改进方向\")\n",
    "\n",
    "print(\"\\n\" + \"=\"*80)"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.8.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
