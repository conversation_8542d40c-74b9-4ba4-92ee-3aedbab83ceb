<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 450px;
            width: 90%;
            backdrop-filter: blur(10px);
        }
        
        .logo {
            text-align: center;
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .login-title {
            text-align: center;
            color: #333;
            font-weight: 700;
            margin-bottom: 2rem;
            font-size: 1.8rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .form-check {
            margin: 1rem 0;
        }
        
        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }
        
        .links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .links a:hover {
            color: #764ba2;
        }
        
        .alert {
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: #f8f9fa;
            transform: translateX(-5px);
        }
        
        @media (max-width: 768px) {
            .login-container {
                padding: 2rem 1.5rem;
            }
            
            .back-link {
                position: relative;
                top: auto;
                left: auto;
                display: block;
                text-align: center;
                margin-bottom: 1rem;
                color: #667eea;
            }
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="{% url 'welcome' %}" class="back-link">
        <i class="fas fa-arrow-left me-2"></i>返回首页
    </a>
    
    <div class="login-container">
        <!-- Logo -->
        <div class="logo">
            <i class="fas fa-shield-virus"></i>
        </div>
        
        <!-- 标题 -->
        <h2 class="login-title">用户登录</h2>
        
        <!-- 消息提示 -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        <!-- 登录表单 -->
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- 用户名 -->
            <div class="form-floating">
                {{ form.username }}
                <label for="{{ form.username.id_for_label }}">
                    <i class="fas fa-user me-2"></i>{{ form.username.label }}
                </label>
                {% if form.username.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.username.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 密码 -->
            <div class="form-floating">
                {{ form.password }}
                <label for="{{ form.password.id_for_label }}">
                    <i class="fas fa-lock me-2"></i>{{ form.password.label }}
                </label>
                {% if form.password.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 记住我 -->
            <div class="form-check">
                {{ form.remember_me }}
                <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                    {{ form.remember_me.label }}
                </label>
            </div>
            
            <!-- 登录按钮 -->
            <button type="submit" class="btn btn-primary btn-login">
                <i class="fas fa-sign-in-alt me-2"></i>登录
            </button>
        </form>
        
        <!-- 链接 -->
        <div class="links">
            <div class="mb-2">
                还没有账户？<a href="{% url 'register' %}">立即注册</a>
            </div>
            <div>
                <a href="{% url 'welcome' %}">返回首页</a>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 自动聚焦到用户名输入框
        document.addEventListener('DOMContentLoaded', function() {
            const usernameInput = document.getElementById('{{ form.username.id_for_label }}');
            if (usernameInput) {
                usernameInput.focus();
            }
        });
        
        // 表单验证增强
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('{{ form.username.id_for_label }}').value.trim();
            const password = document.getElementById('{{ form.password.id_for_label }}').value;
            
            if (!username) {
                e.preventDefault();
                alert('请输入用户名');
                return;
            }
            
            if (!password) {
                e.preventDefault();
                alert('请输入密码');
                return;
            }
        });
    </script>
</body>
</html>
