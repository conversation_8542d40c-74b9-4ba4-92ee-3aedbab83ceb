<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}口罩检测系统{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .upload-area {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #0d6efd;
            background-color: #f8f9fa;
        }
        .upload-area.dragover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .preview-image {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .detection-card {
            transition: transform 0.2s;
        }
        .detection-card:hover {
            transform: translateY(-5px);
        }
        .status-badge {
            font-size: 0.8em;
        }
        .progress-container {
            display: none;
        }
        .result-comparison {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        @media (max-width: 768px) {
            .result-comparison {
                flex-direction: column;
            }
        }

        /* 批量检测样式 */
        .upload-area-batch {
            border: 2px dashed #dee2e6;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            background-color: #f8f9fa;
        }
        .upload-area-batch:hover {
            border-color: #0d6efd;
            background-color: #e7f3ff;
        }
        .upload-area-batch.dragover {
            border-color: #0d6efd;
            background-color: #cce7ff;
        }

        .batch-preview-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            max-height: 300px;
            overflow-y: auto;
        }

        .preview-item {
            position: relative;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            overflow: hidden;
            background: white;
        }

        .preview-item img {
            width: 100%;
            height: 80px;
            object-fit: cover;
        }

        .preview-item .preview-info {
            padding: 5px;
            font-size: 0.75rem;
            text-align: center;
            background: white;
        }

        .preview-item .remove-btn {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: rgba(220, 53, 69, 0.8);
            color: white;
            border: none;
            font-size: 0.7rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-item .remove-btn:hover {
            background: rgba(220, 53, 69, 1);
        }

        .batch-progress-container {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            background-color: #f8f9fa;
        }

        .progress-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .progress-item {
            display: flex;
            align-items: center;
            padding: 8px;
            margin-bottom: 5px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }

        .progress-item .filename {
            flex: 1;
            font-size: 0.9rem;
            margin-right: 10px;
        }

        .progress-item .status {
            font-size: 0.8rem;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .status.pending {
            background-color: #f8f9fa;
            color: #6c757d;
        }

        .status.processing {
            background-color: #fff3cd;
            color: #856404;
        }

        .status.completed {
            background-color: #d1edff;
            color: #0c63e4;
        }

        .status.failed {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{% url 'index' %}">
                <i class="fas fa-mask"></i> 口罩检测系统
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                {% if user.is_authenticated %}
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'index' %}active{% endif %}"
                               href="{% url 'index' %}">
                                <i class="fas fa-home"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'detect' %}active{% endif %}"
                               href="{% url 'detect' %}">
                                <i class="fas fa-search"></i> 检测
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'history' %}active{% endif %}"
                               href="{% url 'history' %}">
                                <i class="fas fa-history"></i> 检测历史
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'batch_history' %}active{% endif %}"
                               href="{% url 'batch_history' %}">
                                <i class="fas fa-images"></i> 批量历史
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'model_management' %}active{% endif %}"
                               href="{% url 'model_management' %}">
                                <i class="fas fa-cogs"></i> 模型管理
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'settings_view' %}active{% endif %}"
                               href="{% url 'settings_view' %}">
                                <i class="fas fa-sliders-h"></i> 设置
                            </a>
                        </li>
                    </ul>

                    <ul class="navbar-nav">
                        <!-- 管理员专用菜单 -->
                        {% if user.is_superuser %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user-shield"></i> 管理员
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="/admin/" target="_blank">
                                        <i class="fas fa-cog"></i> 管理后台
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item" href="{% url 'index' %}?show_all=true">
                                        <i class="fas fa-globe"></i> 查看全站数据
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'history' %}?show_all=true">
                                        <i class="fas fa-list"></i> 全站检测历史
                                    </a></li>
                                </ul>
                            </li>
                        {% endif %}

                        <!-- 用户菜单 -->
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                               data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-user"></i> {{ user.username }}
                                {% if user.is_superuser %}
                                    <span class="badge bg-warning ms-1">管理员</span>
                                {% endif %}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{% url 'profile' %}">
                                    <i class="fas fa-user-circle"></i> 个人资料
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{% url 'logout' %}"
                                       onclick="return confirm('确定要退出登录吗？')">
                                    <i class="fas fa-sign-out-alt"></i> 退出登录
                                </a></li>
                            </ul>
                        </li>
                    </ul>
                {% else %}
                    <!-- 未登录用户显示登录链接 -->
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt"></i> 登录
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="{% url 'register' %}">
                                <i class="fas fa-user-plus"></i> 注册
                            </a>
                        </li>
                    </ul>
                {% endif %}
            </div>
        </div>
    </nav>

    <!-- 消息提示 -->
    {% if messages %}
        <div class="container mt-3">
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- 主要内容 -->
    <main class="container my-4">
        {% block content %}{% endblock %}
    </main>

    <!-- 页脚 -->
    <footer class="bg-light text-center py-3 mt-5">
        <div class="container">
            <p class="text-muted mb-0">
                <i class="fas fa-mask"></i> 口罩检测系统 - 基于YOLO深度学习技术
            </p>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
