<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .welcome-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            text-align: center;
            max-width: 600px;
            width: 90%;
            backdrop-filter: blur(10px);
        }
        
        .logo {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .welcome-title {
            color: #333;
            font-weight: 700;
            margin-bottom: 1rem;
            font-size: 2.5rem;
        }
        
        .welcome-subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 2rem;
            line-height: 1.6;
        }
        
        .feature-list {
            text-align: left;
            margin: 2rem 0;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
            padding: 0.5rem;
            border-radius: 10px;
            background: rgba(102, 126, 234, 0.1);
        }
        
        .feature-icon {
            color: #667eea;
            margin-right: 1rem;
            font-size: 1.2rem;
            width: 30px;
        }
        
        .btn-custom {
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            margin: 0.5rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            min-width: 150px;
        }
        
        .btn-primary-custom {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            color: white;
        }
        
        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }
        
        .btn-outline-custom {
            border: 2px solid #667eea;
            color: #667eea;
            background: transparent;
        }
        
        .btn-outline-custom:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .system-info {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 10px;
            font-size: 0.9rem;
            color: #666;
        }
        
        @media (max-width: 768px) {
            .welcome-container {
                padding: 2rem 1.5rem;
            }
            
            .welcome-title {
                font-size: 2rem;
            }
            
            .logo {
                font-size: 3rem;
            }
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <!-- Logo -->
        <div class="logo">
            <i class="fas fa-shield-virus"></i>
        </div>
        
        <!-- 标题 -->
        <h1 class="welcome-title">口罩检测系统</h1>
        <p class="welcome-subtitle">
            基于YOLO深度学习的智能口罩佩戴检测平台<br>
            提供精准检测、智能分析、数据管理等完整功能
        </p>
        
        <!-- 功能特性 -->
        <div class="feature-list">
            <div class="feature-item">
                <i class="fas fa-camera feature-icon"></i>
                <span>智能图像检测 - 支持多种格式图片上传</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-brain feature-icon"></i>
                <span>AI分析报告 - 大模型智能分析检测结果</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-chart-bar feature-icon"></i>
                <span>数据统计管理 - 完整的检测历史和统计</span>
            </div>
            <div class="feature-item">
                <i class="fas fa-cogs feature-icon"></i>
                <span>模型管理 - 支持多种YOLO模型切换</span>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="mt-4">
            <a href="{% url 'login' %}" class="btn btn-custom btn-primary-custom">
                <i class="fas fa-sign-in-alt me-2"></i>登录系统
            </a>
            <a href="{% url 'register' %}" class="btn btn-custom btn-outline-custom">
                <i class="fas fa-user-plus me-2"></i>注册账户
            </a>
        </div>
        
        <!-- 系统信息 -->
        <div class="system-info">
            <i class="fas fa-info-circle me-2"></i>
            基于Django + YOLO12 + Bootstrap构建 | 支持多用户数据隔离 | 企业级安全防护
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
