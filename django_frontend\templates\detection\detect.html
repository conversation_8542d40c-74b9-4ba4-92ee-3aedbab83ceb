{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<!-- 检测模式选择器 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">
                        <i class="fas fa-search"></i> 口罩检测
                    </h4>
                    <div class="btn-group" role="group" aria-label="检测模式">
                        <input type="radio" class="btn-check" name="detectionMode" id="singleMode" value="single" checked>
                        <label class="btn btn-outline-primary" for="singleMode">
                            <i class="fas fa-image"></i> 单张检测
                        </label>
                        <input type="radio" class="btn-check" name="detectionMode" id="batchMode" value="batch">
                        <label class="btn btn-outline-primary" for="batchMode">
                            <i class="fas fa-images"></i> 批量检测
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 上传区域 -->
    <div class="col-lg-8">
        <!-- 单张检测卡片 -->
        <div class="card" id="singleDetectionCard">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-upload"></i> 上传图片进行口罩检测
                </h5>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" action="{% url 'upload_and_detect' %}" id="uploadForm">
                    {% csrf_token %}
                    
                    <!-- 拖拽上传区域 -->
                    <div class="upload-area" id="uploadArea" onclick="document.getElementById('id_original_image').click()">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>拖拽图片到此处或点击选择文件</h5>
                        <p class="text-muted">支持 JPG, PNG, JPEG, BMP 格式，最大 10MB<br>
                        <strong>单张图片：</strong>点击选择或拖拽一张图片<br>
                        <strong>多张图片：</strong>按住Ctrl多选或拖拽多张图片（最多10张）</p>
                        {{ form.original_image }}
                    </div>

                    <!-- 图片预览 -->
                    <div id="imagePreview" class="mt-3" style="display: none;">
                        <!-- 单图片预览 -->
                        <div id="singlePreview" style="display: none;">
                            <div class="text-center">
                                <img id="previewImg" class="preview-image" alt="预览图片" style="max-width: 100%; max-height: 300px; border-radius: 10px;">
                            </div>
                            <div class="mt-2 text-center">
                                <small class="text-muted">已选择 1 张图片</small>
                            </div>
                        </div>

                        <!-- 多图片预览 -->
                        <div id="multiplePreview" style="display: none;">
                            <div class="row" id="previewContainer">
                                <!-- 多图片预览将在这里动态生成 -->
                            </div>
                            <div class="mt-2">
                                <small class="text-muted">已选择 <span id="fileCount">0</span> 张图片</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 检测参数 -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.model_name.id_for_label }}" class="form-label">
                                    {{ form.model_name.label }}
                                    <a href="{% url 'model_management' %}" class="btn btn-sm btn-outline-info ms-2">
                                        <i class="fas fa-cogs"></i> 管理模型
                                    </a>
                                </label>
                                {{ form.model_name }}
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    只显示实际存在的模型文件
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.image_size.id_for_label }}" class="form-label">
                                    {{ form.image_size.label }}
                                </label>
                                {{ form.image_size }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.confidence_threshold.id_for_label }}" class="form-label">
                                    {{ form.confidence_threshold.label }}
                                </label>
                                {{ form.confidence_threshold }}
                                <div class="form-text">{{ form.confidence_threshold.help_text }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="{{ form.iou_threshold.id_for_label }}" class="form-label">
                                    {{ form.iou_threshold.label }}
                                </label>
                                {{ form.iou_threshold }}
                                <div class="form-text">{{ form.iou_threshold.help_text }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 提交按钮 -->
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg" id="submitBtn">
                            <i class="fas fa-search"></i> 开始检测
                        </button>
                    </div>
                    
                    <!-- 进度条 -->
                    <div class="progress-container mt-3">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <p class="text-center mt-2" id="progressText">正在处理...</p>
                    </div>
                </form>
            </div>
        </div>

        <!-- 批量检测卡片 -->
        <div class="card" id="batchDetectionCard" style="display: none;">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images"></i> 批量上传图片进行检测
                </h5>
            </div>
            <div class="card-body">
                <form id="batchUploadForm" enctype="multipart/form-data">
                    {% csrf_token %}

                    <!-- 会话名称 -->
                    <div class="mb-3">
                        <label for="batchSessionName" class="form-label">会话名称（可选）</label>
                        <input type="text" class="form-control" id="batchSessionName"
                               placeholder="为这次批量检测起一个名称">
                        <div class="form-text">便于后续查找和管理</div>
                    </div>

                    <!-- 批量上传区域 -->
                    <div class="upload-area-batch" id="batchUploadArea">
                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                        <h5>拖拽多张图片到此处或点击选择文件</h5>
                        <p class="text-muted">支持同时选择多张图片，最多20张，每张最大10MB</p>
                        <input type="file" id="batchImageInput" multiple accept="image/*" style="display: none;">
                        <button type="button" class="btn btn-primary" onclick="document.getElementById('batchImageInput').click()">
                            <i class="fas fa-folder-open"></i> 选择图片
                        </button>
                    </div>

                    <!-- 多图片预览区域 -->
                    <div id="batchPreviewArea" class="batch-preview-container mt-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">已选择图片 (<span id="selectedCount">0</span>/20)</h6>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="clearAllBtn">
                                <i class="fas fa-trash"></i> 清空所有
                            </button>
                        </div>
                        <div class="preview-grid" id="previewGrid">
                            <!-- 动态生成预览项 -->
                        </div>
                    </div>

                    <!-- 批量检测参数 -->
                    <div class="row mt-4" id="batchParametersSection" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchModelName" class="form-label">检测模型</label>
                                <select class="form-select" id="batchModelName">
                                    {% for model in available_models %}
                                    <option value="{{ model.name }}"{% if forloop.first %} selected{% endif %}>
                                        {{ model.display_name }}
                                    </option>
                                    {% empty %}
                                    <option value="yolo11n-seg.pt">YOLO11n-seg.pt - 默认模型（请确保文件存在）</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchImageSize" class="form-label">图像尺寸</label>
                                <select class="form-select" id="batchImageSize">
                                    <option value="320">320x320 (快速)</option>
                                    <option value="640" selected>640x640 (标准)</option>
                                    <option value="1280">1280x1280 (高精度)</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="batchThresholdsSection" style="display: none;">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchConfidence" class="form-label">置信度阈值</label>
                                <input type="number" class="form-control" id="batchConfidence"
                                       value="0.25" min="0.1" max="1.0" step="0.05">
                                <div class="form-text">检测结果的可信程度</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="batchIou" class="form-label">IOU阈值</label>
                                <input type="number" class="form-control" id="batchIou"
                                       value="0.45" min="0.1" max="1.0" step="0.05">
                                <div class="form-text">重叠检测框的过滤</div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="d-grid" id="batchSubmitSection" style="display: none;">
                        <button type="submit" class="btn btn-primary btn-lg" id="batchSubmitBtn">
                            <i class="fas fa-play"></i> 开始批量检测
                        </button>
                    </div>

                    <!-- 批量进度显示 -->
                    <div id="batchProgressArea" class="batch-progress-container mt-3" style="display: none;">
                        <div class="overall-progress mb-3">
                            <div class="d-flex justify-content-between mb-2">
                                <span><strong>整体进度</strong></span>
                                <span id="overallProgressText">0/0</span>
                            </div>
                            <div class="progress mb-2">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     id="overallProgressBar" style="width: 0%"></div>
                            </div>
                            <div class="text-center">
                                <small id="batchStatusText" class="text-muted">准备开始处理...</small>
                            </div>
                        </div>

                        <div class="individual-progress">
                            <h6>处理详情</h6>
                            <div id="individualProgressList" class="progress-list">
                                <!-- 动态生成单张图片进度 -->
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 侧边栏 -->
    <div class="col-lg-4">
        <!-- 检测说明 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 检测说明
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6><i class="fas fa-upload text-primary"></i> 上传要求</h6>
                    <ul class="small text-muted">
                        <li>支持JPG、PNG、JPEG、BMP格式</li>
                        <li>文件大小不超过10MB</li>
                        <li>建议图片清晰度较高</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-cogs text-success"></i> 参数调整</h6>
                    <ul class="small text-muted">
                        <li>置信度：检测结果的可信程度</li>
                        <li>IOU阈值：重叠检测框的过滤</li>
                        <li>图像尺寸：影响检测精度和速度</li>
                    </ul>
                </div>
                
                <div class="mb-3">
                    <h6><i class="fas fa-eye text-info"></i> 检测类别</h6>
                    <div class="mb-2">
                        <span class="badge bg-success me-2">✓</span>
                        <small>正确戴口罩</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-danger me-2">✗</span>
                        <small>未戴口罩</small>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-warning me-2">⚠</span>
                        <small>错误戴口罩</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 最近检测记录 -->
        {% if recent_records %}
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-clock"></i> 最近检测
                </h6>
            </div>
            <div class="card-body">
                {% for record in recent_records %}
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        {% if record.result_image %}
                            <img src="{{ record.result_image.url }}" 
                                 class="rounded" width="50" height="50" 
                                 style="object-fit: cover;">
                        {% else %}
                            <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                 style="width: 50px; height: 50px;">
                                <i class="fas fa-image text-muted"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <div class="fw-bold">{{ record.total_detections }} 个检测</div>
                        <small class="text-muted">{{ record.upload_time|date:"m-d H:i" }}</small>
                    </div>
                    <div class="flex-shrink-0">
                        <a href="{% url 'detection_result' record.id %}" 
                           class="btn btn-sm btn-outline-primary">查看</a>
                    </div>
                </div>
                {% endfor %}
                
                <div class="d-grid">
                    <a href="{% url 'history' %}" class="btn btn-sm btn-outline-secondary">
                        查看全部历史
                    </a>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
{% load static %}
<script>
$(document).ready(function() {
    // 单张检测相关元素
    const uploadArea = $('#uploadArea');
    const fileInput = $('#imageInput');
    const imagePreview = $('#imagePreview');
    const previewImg = $('#previewImg');
    const uploadForm = $('#uploadForm');
    const submitBtn = $('#submitBtn');
    const progressContainer = $('.progress-container');

    // 批量检测相关元素
    const batchUploadArea = $('#batchUploadArea');
    const batchImageInput = $('#batchImageInput');
    const batchPreviewArea = $('#batchPreviewArea');
    const previewGrid = $('#previewGrid');
    const selectedCount = $('#selectedCount');
    const clearAllBtn = $('#clearAllBtn');
    const batchUploadForm = $('#batchUploadForm');
    const batchSubmitBtn = $('#batchSubmitBtn');
    const batchProgressArea = $('#batchProgressArea');

    // 模式切换相关元素
    const singleModeRadio = $('#singleMode');
    const batchModeRadio = $('#batchMode');
    const singleDetectionCard = $('#singleDetectionCard');
    const batchDetectionCard = $('#batchDetectionCard');

    // 批量检测状态变量
    let selectedFiles = [];
    let currentSessionId = null;
    let progressInterval = null;

    // 模式切换功能
    function switchDetectionMode(mode) {
        if (mode === 'single') {
            singleDetectionCard.show();
            batchDetectionCard.hide();
        } else if (mode === 'batch') {
            singleDetectionCard.hide();
            batchDetectionCard.show();
        }
    }

    // 监听模式切换
    $('input[name="detectionMode"]').on('change', function() {
        switchDetectionMode($(this).val());
    });
    
    // 拖拽上传功能
    uploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });
    
    uploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });
    
    uploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
        
        const files = e.originalEvent.dataTransfer.files;
        if (files.length > 0) {
            // 显示预览
            showFilePreview(files);
        }
    });

    function showFilePreview(files) {
        const previewContainer = $('#previewContainer');
        const imagePreview = $('#imagePreview');
        const singlePreview = $('#singlePreview');
        const multiplePreview = $('#multiplePreview');

        if (files.length === 1) {
            // 单张图片预览
            const file = files[0];
            const reader = new FileReader();
            reader.onload = function(e) {
                $('#previewImg').attr('src', e.target.result);
                singlePreview.show();
                multiplePreview.hide();
                imagePreview.fadeIn();
            };
            reader.readAsDataURL(file);
        } else if (files.length > 1) {
            // 多张图片预览
            previewContainer.empty();
            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                const reader = new FileReader();
                reader.onload = function(e) {
                    const previewHtml = `
                        <div class="col-md-3 col-sm-4 col-6 mb-3">
                            <div class="position-relative">
                                <img src="${e.target.result}" class="img-fluid rounded"
                                     style="height: 150px; object-fit: cover; width: 100%;" alt="预览图片 ${i + 1}">
                                <div class="text-center mt-1">
                                    <small class="text-muted">${file.name}</small>
                                </div>
                            </div>
                        </div>
                    `;
                    previewContainer.append(previewHtml);
                };
                reader.readAsDataURL(file);
            }
            $('#fileCount').text(files.length);
            singlePreview.hide();
            multiplePreview.show();
            imagePreview.fadeIn();
        }
    }
    
    // 表单提交
    uploadForm.on('submit', function() {
        if (!fileInput[0].files.length) {
            alert('请先选择要检测的图片');
            return false;
        }
        
        submitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 检测中...');
        progressContainer.show();
        
        // 模拟进度条
        let progress = 0;
        const progressBar = $('.progress-bar');
        const progressText = $('#progressText');
        
        const interval = setInterval(function() {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressBar.css('width', progress + '%');
            
            if (progress < 30) {
                progressText.text('正在上传图片...');
            } else if (progress < 60) {
                progressText.text('正在加载AI模型...');
            } else {
                progressText.text('正在进行检测分析...');
            }
        }, 500);
        
        // 清理定时器（实际提交后会跳转页面）
        setTimeout(function() {
            clearInterval(interval);
        }, 30000);
    });

    // ==================== 批量检测功能 ====================

    // 批量文件选择处理
    batchImageInput.on('change', function() {
        const files = Array.from(this.files);
        handleBatchFiles(files);
    });

    // 批量拖拽上传功能
    batchUploadArea.on('dragover', function(e) {
        e.preventDefault();
        $(this).addClass('dragover');
    });

    batchUploadArea.on('dragleave', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');
    });

    batchUploadArea.on('drop', function(e) {
        e.preventDefault();
        $(this).removeClass('dragover');

        const files = Array.from(e.originalEvent.dataTransfer.files);
        handleBatchFiles(files);
    });

    // 点击上传区域
    batchUploadArea.on('click', function() {
        batchImageInput.click();
    });

    // 处理批量文件
    function handleBatchFiles(files) {
        // 过滤图片文件
        const imageFiles = files.filter(file => file.type.startsWith('image/'));

        if (imageFiles.length === 0) {
            alert('请选择有效的图片文件');
            return;
        }

        // 检查文件数量限制
        if (selectedFiles.length + imageFiles.length > 20) {
            alert(`最多只能选择20张图片，当前已选择${selectedFiles.length}张`);
            return;
        }

        // 检查文件大小
        const maxSize = 10 * 1024 * 1024; // 10MB
        const oversizedFiles = imageFiles.filter(file => file.size > maxSize);
        if (oversizedFiles.length > 0) {
            alert(`以下文件超过10MB限制：\n${oversizedFiles.map(f => f.name).join('\n')}`);
            return;
        }

        // 添加到选中文件列表
        imageFiles.forEach(file => {
            if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                selectedFiles.push(file);
            }
        });

        updateBatchPreview();
        updateBatchUI();
    }

    // 更新批量预览
    function updateBatchPreview() {
        previewGrid.empty();
        selectedCount.text(selectedFiles.length);

        selectedFiles.forEach((file, index) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                const previewItem = $(`
                    <div class="preview-item" data-index="${index}">
                        <img src="${e.target.result}" alt="${file.name}">
                        <div class="preview-info">
                            <div class="filename">${file.name.length > 15 ? file.name.substring(0, 12) + '...' : file.name}</div>
                            <div class="filesize">${(file.size / 1024 / 1024).toFixed(1)}MB</div>
                        </div>
                        <button type="button" class="remove-btn" onclick="removeBatchFile(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `);
                previewGrid.append(previewItem);
            };
            reader.readAsDataURL(file);
        });

        if (selectedFiles.length > 0) {
            batchPreviewArea.show();
        } else {
            batchPreviewArea.hide();
        }
    }

    // 移除批量文件
    window.removeBatchFile = function(index) {
        selectedFiles.splice(index, 1);
        updateBatchPreview();
        updateBatchUI();
    };

    // 清空所有文件
    clearAllBtn.on('click', function() {
        selectedFiles = [];
        updateBatchPreview();
        updateBatchUI();
    });

    // 更新批量检测UI状态
    function updateBatchUI() {
        if (selectedFiles.length > 0) {
            $('#batchParametersSection').show();
            $('#batchThresholdsSection').show();
            $('#batchSubmitSection').show();
        } else {
            $('#batchParametersSection').hide();
            $('#batchThresholdsSection').hide();
            $('#batchSubmitSection').hide();
        }
    }

    // 批量表单提交
    batchUploadForm.on('submit', function(e) {
        e.preventDefault();

        if (selectedFiles.length === 0) {
            alert('请先选择要检测的图片');
            return;
        }

        startBatchDetection();
    });

    // 开始批量检测
    function startBatchDetection() {
        // 禁用提交按钮
        batchSubmitBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 正在提交...');

        // 准备表单数据
        const formData = new FormData();
        formData.append('csrfmiddlewaretoken', $('[name=csrfmiddlewaretoken]').val());
        formData.append('session_name', $('#batchSessionName').val() || '');
        formData.append('model_name', $('#batchModelName').val());
        formData.append('confidence', $('#batchConfidence').val());
        formData.append('iou', $('#batchIou').val());
        formData.append('imgsz', $('#batchImageSize').val());

        // 添加所有选中的文件
        selectedFiles.forEach((file, index) => {
            formData.append('images', file);
        });

        // 发送批量检测请求
        $.ajax({
            url: '/api/batch-detect/',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            timeout: 120000, // 提交请求超时时间增加到2分钟（主要用于文件上传）
            success: function(response) {
                if (response.success) {
                    currentSessionId = response.session_id;
                    showBatchProgress();
                    startProgressPolling();
                } else {
                    alert('批量检测提交失败：' + (response.error || '未知错误'));
                    resetBatchForm();
                }
            },
            error: function(xhr, status, error) {
                console.error('批量检测请求失败:', error);
                let errorMsg = '批量检测提交失败';
                if (status === 'timeout') {
                    errorMsg = '请求超时，请检查网络连接或减少图片数量';
                } else if (xhr.responseJSON && xhr.responseJSON.error) {
                    errorMsg = xhr.responseJSON.error;
                }
                alert(errorMsg);
                resetBatchForm();
            }
        });
    }

    // 显示批量进度
    function showBatchProgress() {
        batchProgressArea.show();
        $('#batchSubmitSection').hide();

        // 初始化进度显示
        const progressList = $('#individualProgressList');
        progressList.empty();

        selectedFiles.forEach((file, index) => {
            const progressItem = $(`
                <div class="progress-item" data-index="${index}">
                    <div class="filename">${file.name}</div>
                    <div class="status pending">等待中</div>
                </div>
            `);
            progressList.append(progressItem);
        });
    }

    // 开始进度轮询
    function startProgressPolling() {
        if (!currentSessionId) return;

        progressInterval = setInterval(function() {
            checkBatchProgress();
        }, 2000); // 每2秒检查一次进度
    }

    // 检查批量进度
    function checkBatchProgress() {
        if (!currentSessionId) return;

        $.ajax({
            url: `/api/batch-progress/${currentSessionId}/`,
            type: 'GET',
            timeout: 30000, // 进度查询超时时间30秒
            success: function(response) {
                updateProgressDisplay(response);

                // 检查是否完成
                if (response.status === 'completed' || response.status === 'partial_completed' || response.status === 'failed') {
                    clearInterval(progressInterval);
                    onBatchCompleted(response);
                }
            },
            error: function(xhr, status, error) {
                console.error('获取进度失败:', error);

                // 如果是超时错误，继续轮询，不中断处理
                if (status !== 'timeout') {
                    // 非超时错误，可能需要停止轮询
                    console.warn('进度查询出现非超时错误，继续尝试...');
                }
                // 超时错误不做特殊处理，继续下次轮询
            }
        });
    }

    // 更新进度显示
    function updateProgressDisplay(data) {
        // 更新整体进度
        const overallProgress = data.overall_progress || 0;
        $('#overallProgressBar').css('width', overallProgress + '%');
        $('#overallProgressText').text(`${data.completed_images + data.failed_images}/${data.total_images}`);

        // 更新状态文本
        let statusText = '';
        switch(data.status) {
            case 'pending':
                statusText = '等待开始处理...';
                break;
            case 'processing':
                statusText = `正在处理中... (${data.completed_images} 完成, ${data.failed_images} 失败)`;
                break;
            case 'completed':
                statusText = `处理完成! 全部 ${data.completed_images} 张图片处理成功`;
                break;
            case 'partial_completed':
                statusText = `处理完成! ${data.completed_images} 成功, ${data.failed_images} 失败`;
                break;
            case 'failed':
                statusText = `处理失败! ${data.failed_images} 张图片处理失败`;
                break;
            default:
                statusText = '状态未知';
        }
        $('#batchStatusText').text(statusText);

        // 更新单个文件状态
        if (data.records) {
            data.records.forEach(record => {
                const progressItem = $(`.progress-item[data-index="${record.batch_index}"]`);
                const statusElement = progressItem.find('.status');

                statusElement.removeClass('pending processing completed failed');
                statusElement.addClass(record.status);

                switch (record.status) {
                    case 'pending':
                        statusElement.text('等待中');
                        break;
                    case 'processing':
                        statusElement.text('处理中...');
                        break;
                    case 'completed':
                        let completedText = '完成';
                        if (record.total_detections > 0) {
                            completedText += ` (${record.total_detections}个检测)`;
                        }
                        if (record.processing_time) {
                            completedText += ` ${record.processing_time.toFixed(1)}s`;
                        }
                        statusElement.text(completedText);
                        break;
                    case 'failed':
                        let failedText = '失败';
                        if (record.error_message) {
                            failedText += ` - ${record.error_message.substring(0, 30)}...`;
                        }
                        statusElement.text(failedText);
                        statusElement.attr('title', record.error_message || '处理失败');
                        break;
                }
            });
        }
    }

    // 批量检测完成
    function onBatchCompleted(data) {
        batchSubmitBtn.prop('disabled', false).html('<i class="fas fa-play"></i> 开始批量检测');

        // 显示完成消息
        let message = '';
        let alertClass = 'alert-success';

        if (data.status === 'completed') {
            message = `🎉 批量检测完成！成功处理了 ${data.completed_images} 张图片。`;
            alertClass = 'alert-success';
        } else if (data.status === 'partial_completed') {
            message = `⚠️ 批量检测完成！成功处理了 ${data.completed_images} 张图片，${data.failed_images} 张图片处理失败。`;
            alertClass = 'alert-warning';
        } else {
            message = `❌ 批量检测失败！${data.failed_images} 张图片处理失败。`;
            alertClass = 'alert-danger';
        }

        // 显示美化的提示消息
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <strong>${message}</strong>
                <br><small>页面将在3秒后自动跳转到结果页面...</small>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // 在进度区域上方显示提示
        $('#batchProgressArea').before(alertHtml);

        // 跳转到批量结果页面
        setTimeout(function() {
            const sessionId = data.session_id || currentSessionId;
            if (sessionId) {
                window.location.href = `/app/batch-result/${sessionId}/`;
            }
        }, 3000);
    }

    // 重置批量表单
    function resetBatchForm() {
        batchSubmitBtn.prop('disabled', false).html('<i class="fas fa-play"></i> 开始批量检测');
        batchProgressArea.hide();
        $('#batchSubmitSection').show();
    }
});
</script>

<style>
/* 批量进度显示样式 */
.batch-progress-container {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
}

.progress-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    margin-bottom: 0.25rem;
    background: white;
    border-radius: 0.25rem;
    border: 1px solid #e9ecef;
}

.progress-item .filename {
    flex: 1;
    font-weight: 500;
    color: #495057;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-right: 1rem;
}

.progress-item .status {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    min-width: 80px;
    text-align: center;
}

.progress-item .status.pending {
    background-color: #e9ecef;
    color: #6c757d;
}

.progress-item .status.processing {
    background-color: #cff4fc;
    color: #055160;
    animation: pulse 1.5s ease-in-out infinite alternate;
}

.progress-item .status.completed {
    background-color: #d1e7dd;
    color: #0f5132;
}

.progress-item .status.failed {
    background-color: #f8d7da;
    color: #721c24;
}

@keyframes pulse {
    from { opacity: 1; }
    to { opacity: 0.5; }
}

#batchStatusText {
    font-size: 0.9rem;
    font-weight: 500;
}
</style>
{% endblock %}
