#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :model_evaluation.py
# @Time      :2025/7/29
# <AUTHOR> Assistant
# @Project   :FaceMaskDetection
# @Function  :完整的模型测试与评估脚本，包括交叉验证、性能对比和错误分析

import os
import sys
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import cv2
import yaml
import json
from datetime import datetime
import warnings
import argparse
from sklearn.model_selection import KFold
from sklearn.metrics import confusion_matrix, classification_report

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 添加项目路径
current_path = Path(__file__).parent.parent.resolve()
utils_path = current_path / 'utils'
if str(current_path) not in sys.path:
    sys.path.insert(0, str(current_path))
if str(utils_path) not in sys.path:
    sys.path.insert(1, str(utils_path))

from ultralytics import YOLO

def setup_paths():
    """设置路径和配置"""
    CONFIG_DIR = current_path / 'configs'
    MODEL_DIR = current_path / 'models' / 'checkpoints'
    DATA_DIR = current_path / 'data'
    RESULTS_DIR = current_path / 'evaluation_results'
    RESULTS_DIR.mkdir(exist_ok=True)
    
    return CONFIG_DIR, MODEL_DIR, DATA_DIR, RESULTS_DIR

def load_config(CONFIG_DIR):
    """加载数据配置"""
    with open(CONFIG_DIR / 'data.yaml', 'r', encoding='utf-8') as f:
        data_config = yaml.safe_load(f)
    
    class_names = data_config['names']
    num_classes = data_config['nc']
    
    print("数据配置:")
    for key, value in data_config.items():
        print(f"  {key}: {value}")
    print(f"\n类别数量: {num_classes}")
    print(f"类别名称: {class_names}")
    
    return data_config, class_names, num_classes

def get_best_model(MODEL_DIR):
    """获取最佳模型"""
    model_files = list(MODEL_DIR.glob('*.pt'))
    print("可用的模型文件:")
    for i, model_file in enumerate(model_files):
        print(f"  {i}: {model_file.name}")
    
    # 选择最新的best模型
    best_models = [f for f in model_files if 'best' in f.name]
    if best_models:
        main_model_path = sorted(best_models)[-1]
        print(f"\n选择主要评估模型: {main_model_path.name}")
    else:
        main_model_path = model_files[0] if model_files else None
        print(f"\n未找到best模型，使用: {main_model_path.name if main_model_path else 'None'}")
    
    return main_model_path, model_files

def perform_cross_validation(model_path, data_yaml, data_config, k_folds=3):
    """执行K折交叉验证"""
    print(f"\n{'='*60}")
    print(f"开始 {k_folds} 折交叉验证...")
    print(f"{'='*60}")
    
    cv_results = []
    
    for fold in range(k_folds):
        print(f"\n=== 第 {fold + 1} 折验证 ===")
        
        # 加载模型
        model = YOLO(str(model_path))
        
        # 在验证集上评估
        results = model.val(
            data=str(data_yaml),
            split='val',
            save=False,
            verbose=False
        )
        
        # 提取关键指标
        metrics = {
            'fold': fold + 1,
            'mAP50': float(results.box.map50),
            'mAP50_95': float(results.box.map),
            'precision': float(results.box.mp),
            'recall': float(results.box.mr),
            'fitness': float(results.fitness)
        }
        
        cv_results.append(metrics)
        print(f"mAP@0.5: {metrics['mAP50']:.4f}")
        print(f"mAP@0.5:0.95: {metrics['mAP50_95']:.4f}")
        print(f"Precision: {metrics['precision']:.4f}")
        print(f"Recall: {metrics['recall']:.4f}")
    
    # 转换为DataFrame便于分析
    cv_df = pd.DataFrame(cv_results)
    print(f"\n{'='*60}")
    print("交叉验证结果汇总")
    print(f"{'='*60}")
    print(cv_df)
    
    # 计算统计信息
    print(f"\n{'='*60}")
    print("统计信息")
    print(f"{'='*60}")
    for metric in ['mAP50', 'mAP50_95', 'precision', 'recall', 'fitness']:
        mean_val = cv_df[metric].mean()
        std_val = cv_df[metric].std()
        print(f"{metric}: {mean_val:.4f} ± {std_val:.4f}")
    
    return cv_df

def compare_models(model_files, data_yaml, data_config):
    """比较多个模型的性能"""
    print(f"\n{'='*60}")
    print("开始模型性能对比...")
    print(f"{'='*60}")

    comparison_results = []

    # 检查测试集是否存在，如果不存在则使用验证集
    test_images_dir = Path(data_config['test'])
    val_images_dir = Path(data_config['val'])

    if not test_images_dir.exists() or len(list(test_images_dir.glob('*.jpg')) + list(test_images_dir.glob('*.png'))) == 0:
        print("⚠️  测试集不存在或为空，使用验证集进行模型对比")
        eval_split = 'val'
    else:
        eval_split = 'test'
        print(f"✅ 使用 {eval_split} 集进行模型对比")

    for i, model_path in enumerate(model_files[:3]):  # 比较前3个模型
        print(f"\n📊 评估模型 {i+1}/{min(3, len(model_files))}: {model_path.name}")

        try:
            # 加载模型
            model = YOLO(str(model_path))

            # 在指定数据集上评估
            results = model.val(
                data=str(data_yaml),
                split=eval_split,
                save=False,
                verbose=False
            )

            # 安全地提取性能指标
            def safe_extract(value, default=0.0):
                try:
                    return float(value) if value is not None else default
                except (TypeError, ValueError):
                    return default

            # 提取性能指标
            model_results = {
                'model_name': model_path.stem,
                'model_size_mb': model_path.stat().st_size / (1024 * 1024),
                'mAP50': safe_extract(results.box.map50),
                'mAP50_95': safe_extract(results.box.map),
                'precision': safe_extract(results.box.mp),
                'recall': safe_extract(results.box.mr),
                'fitness': safe_extract(results.fitness),
                'inference_time': safe_extract(results.speed.get('inference', 0)),
                'preprocess_time': safe_extract(results.speed.get('preprocess', 0)),
                'postprocess_time': safe_extract(results.speed.get('postprocess', 0)),
                'eval_split': eval_split
            }

            # 计算总处理时间
            model_results['total_time'] = (model_results['preprocess_time'] +
                                         model_results['inference_time'] +
                                         model_results['postprocess_time'])

            # 计算效率指标 (mAP per MB)
            if model_results['model_size_mb'] > 0:
                model_results['efficiency_mAP50_per_MB'] = model_results['mAP50'] / model_results['model_size_mb']
                model_results['efficiency_mAP50_95_per_MB'] = model_results['mAP50_95'] / model_results['model_size_mb']
            else:
                model_results['efficiency_mAP50_per_MB'] = 0
                model_results['efficiency_mAP50_95_per_MB'] = 0

            # 计算速度效率指标 (mAP per ms)
            if model_results['inference_time'] > 0:
                model_results['speed_efficiency'] = model_results['mAP50'] / model_results['inference_time']
            else:
                model_results['speed_efficiency'] = 0

            comparison_results.append(model_results)

            print(f"  ✅ mAP@0.5: {model_results['mAP50']:.4f}")
            print(f"  ✅ mAP@0.5:0.95: {model_results['mAP50_95']:.4f}")
            print(f"  ⚡ 推理时间: {model_results['inference_time']:.2f}ms")
            print(f"  💾 模型大小: {model_results['model_size_mb']:.2f}MB")
            print(f"  🎯 效率指标: {model_results['efficiency_mAP50_per_MB']:.4f} mAP/MB")
            print(f"  🚀 速度效率: {model_results['speed_efficiency']:.4f} mAP/ms")

        except Exception as e:
            print(f"  ❌ 评估模型 {model_path.name} 时出错: {e}")
            # 添加一个错误记录
            error_results = {
                'model_name': model_path.stem,
                'model_size_mb': model_path.stat().st_size / (1024 * 1024),
                'mAP50': 0.0,
                'mAP50_95': 0.0,
                'precision': 0.0,
                'recall': 0.0,
                'fitness': 0.0,
                'inference_time': 0.0,
                'preprocess_time': 0.0,
                'postprocess_time': 0.0,
                'total_time': 0.0,
                'efficiency_mAP50_per_MB': 0.0,
                'efficiency_mAP50_95_per_MB': 0.0,
                'speed_efficiency': 0.0,
                'eval_split': eval_split,
                'error': str(e)
            }
            comparison_results.append(error_results)

    if not comparison_results:
        print("❌ 没有成功评估任何模型")
        return None

    # 转换为DataFrame
    comparison_df = pd.DataFrame(comparison_results)

    # 过滤掉错误的结果用于显示
    valid_results = comparison_df[comparison_df['mAP50'] > 0]

    print(f"\n{'='*60}")
    print("模型性能对比结果")
    print(f"{'='*60}")

    if len(valid_results) > 0:
        # 显示主要指标
        display_cols = ['model_name', 'mAP50', 'mAP50_95', 'precision', 'recall', 'inference_time', 'model_size_mb']
        print("\n📊 主要性能指标:")
        print(valid_results[display_cols].to_string(index=False, float_format='%.4f'))

        # 显示效率指标
        efficiency_cols = ['model_name', 'efficiency_mAP50_per_MB', 'speed_efficiency']
        print(f"\n🎯 效率指标:")
        print(valid_results[efficiency_cols].to_string(index=False, float_format='%.4f'))

        # 找出最佳模型
        print(f"\n🏆 最佳模型分析:")
        best_mAP = valid_results.loc[valid_results['mAP50'].idxmax()]
        fastest = valid_results.loc[valid_results['inference_time'].idxmin()]
        most_efficient = valid_results.loc[valid_results['efficiency_mAP50_per_MB'].idxmax()]

        print(f"  🥇 最高 mAP@0.5: {best_mAP['model_name']} ({best_mAP['mAP50']:.4f})")
        print(f"  ⚡ 最快推理: {fastest['model_name']} ({fastest['inference_time']:.2f}ms)")
        print(f"  🎯 最高效率: {most_efficient['model_name']} ({most_efficient['efficiency_mAP50_per_MB']:.4f} mAP/MB)")

    else:
        print("❌ 没有有效的评估结果")

    # 显示错误信息
    error_results = comparison_df[comparison_df['mAP50'] == 0]
    if len(error_results) > 0:
        print(f"\n⚠️  评估失败的模型:")
        for _, row in error_results.iterrows():
            if 'error' in row:
                print(f"  - {row['model_name']}: {row['error']}")

    return comparison_df

def analyze_prediction_errors(model, data_config, num_samples=30):
    """分析模型预测错误的案例"""
    print(f"\n{'='*60}")
    print("开始错误分析...")
    print(f"{'='*60}")
    
    # 获取测试图像
    test_images_dir = Path(data_config['test'])
    test_images = list(test_images_dir.glob('*.jpg')) + list(test_images_dir.glob('*.png'))
    
    if len(test_images) == 0:
        print("未找到测试图像，使用验证集进行错误分析")
        test_images_dir = Path(data_config['val'])
        test_images = list(test_images_dir.glob('*.jpg')) + list(test_images_dir.glob('*.png'))
    
    print(f"找到 {len(test_images)} 张测试图像")
    
    # 随机选择样本进行分析
    np.random.seed(42)
    sample_images = np.random.choice(test_images, min(num_samples, len(test_images)), replace=False)
    
    error_analysis = {
        'false_positives': [],
        'false_negatives': [],
        'misclassifications': [],
        'low_confidence': [],
        'high_confidence_errors': []
    }
    
    confidence_threshold = 0.5
    
    for img_path in sample_images:
        # 获取真实标签
        label_path = img_path.parent.parent / 'labels' / f"{img_path.stem}.txt"
        
        if not label_path.exists():
            continue
            
        # 读取真实标签
        with open(label_path, 'r') as f:
            gt_lines = f.readlines()
        
        gt_boxes = []
        for line in gt_lines:
            parts = line.strip().split()
            if len(parts) >= 5:
                gt_boxes.append({
                    'class': int(parts[0]),
                    'bbox': [float(x) for x in parts[1:5]]
                })
        
        # 模型预测
        results = model.predict(str(img_path), conf=0.1, verbose=False)
        
        if len(results) > 0 and results[0].boxes is not None:
            pred_boxes = []
            for i in range(len(results[0].boxes)):
                box = results[0].boxes[i]
                pred_boxes.append({
                    'class': int(box.cls.cpu().numpy()),
                    'confidence': float(box.conf.cpu().numpy()),
                    'bbox': box.xywhn.cpu().numpy().tolist()[0]
                })
        else:
            pred_boxes = []
        
        # 分析错误类型
        img_analysis = {
            'image_path': str(img_path),
            'gt_count': len(gt_boxes),
            'pred_count': len([p for p in pred_boxes if p['confidence'] > confidence_threshold]),
            'gt_boxes': gt_boxes,
            'pred_boxes': pred_boxes
        }
        
        # 检测假阳性
        high_conf_preds = [p for p in pred_boxes if p['confidence'] > confidence_threshold]
        if len(high_conf_preds) > len(gt_boxes):
            error_analysis['false_positives'].append(img_analysis)
        
        # 检测假阴性
        if len(high_conf_preds) < len(gt_boxes):
            error_analysis['false_negatives'].append(img_analysis)
        
        # 检测低置信度预测
        low_conf_preds = [p for p in pred_boxes if p['confidence'] < confidence_threshold]
        if len(low_conf_preds) > 0:
            error_analysis['low_confidence'].append(img_analysis)
    
    print(f"\n{'='*60}")
    print("错误分析结果")
    print(f"{'='*60}")
    print(f"假阳性案例: {len(error_analysis['false_positives'])}")
    print(f"假阴性案例: {len(error_analysis['false_negatives'])}")
    print(f"低置信度预测: {len(error_analysis['low_confidence'])}")
    print(f"高置信度错误: {len(error_analysis['high_confidence_errors'])}")
    
    return error_analysis

def generate_confusion_matrix(model, data_config, class_names, num_classes, split='test'):
    """生成混淆矩阵"""
    print(f"\n{'='*60}")
    print(f"生成 {split} 集的混淆矩阵...")
    print(f"{'='*60}")

    # 获取图像路径
    if split == 'test':
        images_dir = Path(data_config['test'])
    else:
        images_dir = Path(data_config['val'])

    image_files = list(images_dir.glob('*.jpg')) + list(images_dir.glob('*.png'))

    if len(image_files) == 0:
        print(f"未找到 {split} 图像")
        return None

    y_true = []
    y_pred = []

    for img_path in image_files[:100]:  # 限制样本数量以提高速度
        # 获取真实标签
        label_path = img_path.parent.parent / 'labels' / f"{img_path.stem}.txt"

        if not label_path.exists():
            continue

        # 读取真实标签
        with open(label_path, 'r') as f:
            gt_lines = f.readlines()

        if not gt_lines:
            continue

        # 获取主要类别（第一个检测到的目标）
        gt_class = int(gt_lines[0].strip().split()[0])

        # 模型预测
        results = model.predict(str(img_path), conf=0.5, verbose=False)

        if len(results) > 0 and results[0].boxes is not None and len(results[0].boxes) > 0:
            # 获取置信度最高的预测
            confidences = results[0].boxes.conf.cpu().numpy()
            best_idx = np.argmax(confidences)
            pred_class = int(results[0].boxes.cls[best_idx].cpu().numpy())
        else:
            pred_class = -1  # 未检测到目标

        y_true.append(gt_class)
        y_pred.append(pred_class)

    if len(y_true) == 0:
        print("没有有效的预测结果")
        return None

    # 生成混淆矩阵
    cm = confusion_matrix(y_true, y_pred, labels=list(range(num_classes)))

    # 生成分类报告
    report = classification_report(y_true, y_pred, target_names=class_names, output_dict=True)

    print(f"\n{'='*60}")
    print("分类报告")
    print(f"{'='*60}")
    print(classification_report(y_true, y_pred, target_names=class_names))

    return cm, report

def visualize_results(cv_df, comparison_df, error_analysis, cm, RESULTS_DIR):
    """可视化所有结果"""
    print(f"\n{'='*60}")
    print("生成可视化图表...")
    print(f"{'='*60}")

    # 1. 交叉验证结果可视化
    if cv_df is not None:
        fig, axes = plt.subplots(2, 3, figsize=(15, 10))
        fig.suptitle('交叉验证结果分析', fontsize=16, fontweight='bold')

        metrics = ['mAP50', 'mAP50_95', 'precision', 'recall', 'fitness']

        for i, metric in enumerate(metrics):
            row, col = i // 3, i % 3
            axes[row, col].boxplot(cv_df[metric], labels=[metric])
            axes[row, col].set_title(f'{metric} 分布')
            axes[row, col].grid(True, alpha=0.3)

            # 添加均值线
            mean_val = cv_df[metric].mean()
            axes[row, col].axhline(y=mean_val, color='red', linestyle='--', alpha=0.7, label=f'均值: {mean_val:.3f}')
            axes[row, col].legend()

        # 删除多余的子图
        axes[1, 2].remove()

        plt.tight_layout()
        plt.savefig(RESULTS_DIR / 'cross_validation_results.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 保存交叉验证结果
        cv_df.to_csv(RESULTS_DIR / 'cross_validation_results.csv', index=False)
        print(f"交叉验证结果已保存")

    # 2. 模型性能对比可视化
    if comparison_df is not None and len(comparison_df) > 0:
        # 过滤有效数据（排除错误的结果）
        valid_comparison = comparison_df[comparison_df['mAP50'] > 0].copy()

        if len(valid_comparison) == 0:
            print("⚠️  没有有效的模型对比数据，跳过可视化")
        elif len(valid_comparison) == 1:
            print("ℹ️  只有一个有效模型，生成单模型分析图表")

            # 单模型分析图表
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'模型分析: {valid_comparison.iloc[0]["model_name"]}', fontsize=16, fontweight='bold')

            model_data = valid_comparison.iloc[0]

            # 性能指标雷达图数据
            metrics = ['mAP50', 'mAP50_95', 'precision', 'recall']
            values = [model_data[m] for m in metrics]

            # 性能指标条形图
            axes[0, 0].bar(metrics, values, alpha=0.8, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
            axes[0, 0].set_title('性能指标')
            axes[0, 0].set_ylabel('分数')
            axes[0, 0].tick_params(axis='x', rotation=45)
            axes[0, 0].grid(True, alpha=0.3)

            # 时间分解
            time_components = ['preprocess_time', 'inference_time', 'postprocess_time']
            time_values = [model_data[t] for t in time_components]
            time_labels = ['预处理', '推理', '后处理']

            axes[0, 1].pie(time_values, labels=time_labels, autopct='%1.1f%%', startangle=90)
            axes[0, 1].set_title('处理时间分解')

            # 效率指标
            efficiency_metrics = ['efficiency_mAP50_per_MB', 'speed_efficiency']
            efficiency_values = [model_data[m] for m in efficiency_metrics]
            efficiency_labels = ['mAP/MB', 'mAP/ms']

            axes[1, 0].bar(efficiency_labels, efficiency_values, alpha=0.8, color=['#9467bd', '#8c564b'])
            axes[1, 0].set_title('效率指标')
            axes[1, 0].set_ylabel('效率分数')
            axes[1, 0].grid(True, alpha=0.3)

            # 模型信息文本
            info_text = f"""
模型信息:
• 大小: {model_data['model_size_mb']:.2f} MB
• mAP@0.5: {model_data['mAP50']:.4f}
• mAP@0.5:0.95: {model_data['mAP50_95']:.4f}
• 推理时间: {model_data['inference_time']:.2f} ms
• 总处理时间: {model_data['total_time']:.2f} ms
• 效率: {model_data['efficiency_mAP50_per_MB']:.4f} mAP/MB
            """
            axes[1, 1].text(0.1, 0.5, info_text, transform=axes[1, 1].transAxes,
                           fontsize=10, verticalalignment='center',
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
            axes[1, 1].set_xlim(0, 1)
            axes[1, 1].set_ylim(0, 1)
            axes[1, 1].axis('off')
            axes[1, 1].set_title('模型详细信息')

            plt.tight_layout()
            plt.savefig(RESULTS_DIR / 'single_model_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()

        else:
            print(f"📊 生成 {len(valid_comparison)} 个模型的对比图表")

            # 多模型对比图表
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle('模型性能对比分析', fontsize=16, fontweight='bold')

            # mAP对比
            x = range(len(valid_comparison))
            width = 0.35
            axes[0, 0].bar([i - width/2 for i in x], valid_comparison['mAP50'], width, label='mAP@0.5', alpha=0.8)
            axes[0, 0].bar([i + width/2 for i in x], valid_comparison['mAP50_95'], width, label='mAP@0.5:0.95', alpha=0.8)
            axes[0, 0].set_xlabel('模型')
            axes[0, 0].set_ylabel('mAP')
            axes[0, 0].set_title('mAP 对比')
            axes[0, 0].set_xticks(x)
            axes[0, 0].set_xticklabels(valid_comparison['model_name'], rotation=45)
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)

            # Precision vs Recall
            scatter1 = axes[0, 1].scatter(valid_comparison['recall'], valid_comparison['precision'],
                                        s=100, alpha=0.7, c=valid_comparison['mAP50'], cmap='viridis')
            for i, model_name in enumerate(valid_comparison['model_name']):
                axes[0, 1].annotate(model_name, (valid_comparison['recall'].iloc[i], valid_comparison['precision'].iloc[i]),
                                   xytext=(5, 5), textcoords='offset points', fontsize=8)
            axes[0, 1].set_xlabel('Recall')
            axes[0, 1].set_ylabel('Precision')
            axes[0, 1].set_title('Precision vs Recall\n(颜色=mAP@0.5)')
            axes[0, 1].grid(True, alpha=0.3)
            plt.colorbar(scatter1, ax=axes[0, 1])

            # 推理时间对比
            bars = axes[0, 2].bar(valid_comparison['model_name'], valid_comparison['inference_time'],
                                alpha=0.8, color='orange')
            axes[0, 2].set_xlabel('模型')
            axes[0, 2].set_ylabel('推理时间 (ms)')
            axes[0, 2].set_title('推理时间对比')
            axes[0, 2].tick_params(axis='x', rotation=45)
            axes[0, 2].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, time in zip(bars, valid_comparison['inference_time']):
                axes[0, 2].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                               f'{time:.1f}', ha='center', va='bottom', fontsize=8)

            # 模型大小 vs 性能
            scatter2 = axes[1, 0].scatter(valid_comparison['model_size_mb'], valid_comparison['mAP50'],
                                        s=valid_comparison['inference_time']*5, alpha=0.7,
                                        c=valid_comparison['mAP50_95'], cmap='plasma')
            axes[1, 0].set_xlabel('模型大小 (MB)')
            axes[1, 0].set_ylabel('mAP@0.5')
            axes[1, 0].set_title('模型大小 vs 性能\n(气泡大小=推理时间, 颜色=mAP@0.5:0.95)')
            for i, model_name in enumerate(valid_comparison['model_name']):
                axes[1, 0].annotate(model_name, (valid_comparison['model_size_mb'].iloc[i], valid_comparison['mAP50'].iloc[i]),
                                   xytext=(5, 5), textcoords='offset points', fontsize=8)
            plt.colorbar(scatter2, ax=axes[1, 0])
            axes[1, 0].grid(True, alpha=0.3)

            # 效率对比
            axes[1, 1].bar(valid_comparison['model_name'], valid_comparison['efficiency_mAP50_per_MB'],
                          alpha=0.8, color='green')
            axes[1, 1].set_xlabel('模型')
            axes[1, 1].set_ylabel('效率 (mAP/MB)')
            axes[1, 1].set_title('模型效率对比')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)

            # 综合性能雷达图（如果有多个模型）
            if len(valid_comparison) <= 4:  # 最多显示4个模型的雷达图
                from math import pi

                categories = ['mAP50', 'mAP50_95', 'precision', 'recall']
                N = len(categories)

                angles = [n / float(N) * 2 * pi for n in range(N)]
                angles += angles[:1]  # 闭合图形

                ax_radar = plt.subplot(2, 3, 6, projection='polar')

                colors = ['red', 'blue', 'green', 'orange']
                for i, (_, model) in enumerate(valid_comparison.iterrows()):
                    if i >= 4:  # 最多4个模型
                        break
                    values = [model[cat] for cat in categories]
                    values += values[:1]  # 闭合图形

                    ax_radar.plot(angles, values, 'o-', linewidth=2,
                                label=model['model_name'], color=colors[i])
                    ax_radar.fill(angles, values, alpha=0.25, color=colors[i])

                ax_radar.set_xticks(angles[:-1])
                ax_radar.set_xticklabels(categories)
                ax_radar.set_ylim(0, 1)
                ax_radar.set_title('综合性能对比', y=1.08)
                ax_radar.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
            else:
                axes[1, 2].text(0.5, 0.5, f'模型数量过多\n({len(valid_comparison)}个)\n跳过雷达图',
                               ha='center', va='center', transform=axes[1, 2].transAxes,
                               bbox=dict(boxstyle="round,pad=0.3", facecolor="lightgray", alpha=0.5))
                axes[1, 2].set_title('综合性能对比')
                axes[1, 2].axis('off')

            plt.tight_layout()
            plt.savefig(RESULTS_DIR / 'model_comparison.png', dpi=300, bbox_inches='tight')
            plt.close()

        # 保存对比结果
        comparison_df.to_csv(RESULTS_DIR / 'model_comparison.csv', index=False)
        print(f"模型对比结果已保存")

    # 3. 混淆矩阵可视化
    if cm is not None:
        plt.figure(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
        plt.title('混淆矩阵')
        plt.xlabel('预测类别')
        plt.ylabel('真实类别')
        plt.tight_layout()
        plt.savefig(RESULTS_DIR / 'confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.close()
        print(f"混淆矩阵已保存")

def generate_comprehensive_report(main_model_path, data_config, class_names, num_classes,
                                cv_df, comparison_df, error_analysis, classification_report_dict, RESULTS_DIR):
    """生成综合评估报告"""
    print(f"\n{'='*60}")
    print("生成综合评估报告...")
    print(f"{'='*60}")

    report = {
        'timestamp': datetime.now().isoformat(),
        'model_info': {
            'main_model': main_model_path.name if main_model_path else 'N/A',
            'model_size_mb': main_model_path.stat().st_size / (1024 * 1024) if main_model_path else 0
        },
        'dataset_info': {
            'num_classes': num_classes,
            'class_names': class_names,
            'data_config': data_config
        }
    }

    # 添加交叉验证结果
    if cv_df is not None:
        report['cross_validation'] = {
            'mean_mAP50': float(cv_df['mAP50'].mean()),
            'std_mAP50': float(cv_df['mAP50'].std()),
            'mean_mAP50_95': float(cv_df['mAP50_95'].mean()),
            'std_mAP50_95': float(cv_df['mAP50_95'].std()),
            'mean_precision': float(cv_df['precision'].mean()),
            'std_precision': float(cv_df['precision'].std()),
            'mean_recall': float(cv_df['recall'].mean()),
            'std_recall': float(cv_df['recall'].std()),
            'detailed_results': cv_df.to_dict('records')
        }

    # 添加模型对比结果
    if comparison_df is not None:
        report['model_comparison'] = {
            'num_models_compared': len(comparison_df),
            'best_model_mAP50': comparison_df.loc[comparison_df['mAP50'].idxmax()].to_dict(),
            'fastest_model': comparison_df.loc[comparison_df['inference_time'].idxmin()].to_dict(),
            'detailed_comparison': comparison_df.to_dict('records')
        }

    # 添加错误分析结果
    if error_analysis:
        report['error_analysis'] = {
            'false_positives_count': len(error_analysis['false_positives']),
            'false_negatives_count': len(error_analysis['false_negatives']),
            'low_confidence_count': len(error_analysis['low_confidence']),
            'high_confidence_errors_count': len(error_analysis['high_confidence_errors'])
        }

    # 添加分类报告
    if classification_report_dict:
        report['classification_metrics'] = classification_report_dict

    # 保存报告
    report_path = RESULTS_DIR / 'comprehensive_evaluation_report.json'
    with open(report_path, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)

    print(f"报告保存位置: {report_path}")

    # 打印关键结果摘要
    print(f"\n{'='*60}")
    print("关键结果摘要")
    print(f"{'='*60}")
    if 'cross_validation' in report:
        cv = report['cross_validation']
        print(f"交叉验证 mAP@0.5: {cv['mean_mAP50']:.4f} ± {cv['std_mAP50']:.4f}")
        print(f"交叉验证 mAP@0.5:0.95: {cv['mean_mAP50_95']:.4f} ± {cv['std_mAP50_95']:.4f}")

    if 'model_comparison' in report:
        comp = report['model_comparison']
        print(f"最佳模型 (mAP@0.5): {comp['best_model_mAP50']['model_name']} ({comp['best_model_mAP50']['mAP50']:.4f})")
        print(f"最快模型: {comp['fastest_model']['model_name']} ({comp['fastest_model']['inference_time']:.2f}ms)")

    if 'error_analysis' in report:
        err = report['error_analysis']
        print(f"错误分析: FP={err['false_positives_count']}, FN={err['false_negatives_count']}, 高置信度错误={err['high_confidence_errors_count']}")

    return report

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="YOLO模型完整评估脚本")
    parser.add_argument("--cv_folds", type=int, default=3, help="交叉验证折数")
    parser.add_argument("--error_samples", type=int, default=30, help="错误分析样本数")
    parser.add_argument("--skip_cv", action="store_true", help="跳过交叉验证")
    parser.add_argument("--skip_comparison", action="store_true", help="跳过模型对比")
    parser.add_argument("--skip_error", action="store_true", help="跳过错误分析")

    args = parser.parse_args()

    print("="*80)
    print("YOLO 口罩检测模型完整评估分析")
    print("="*80)
    print(f"当前工作目录: {Path.cwd()}")
    print(f"项目根目录: {current_path}")

    # 设置路径
    CONFIG_DIR, MODEL_DIR, DATA_DIR, RESULTS_DIR = setup_paths()

    # 加载配置
    data_config, class_names, num_classes = load_config(CONFIG_DIR)

    # 获取模型
    main_model_path, model_files = get_best_model(MODEL_DIR)

    if not main_model_path:
        print("错误: 未找到可用的模型文件")
        return

    # 加载主模型
    main_model = YOLO(str(main_model_path))
    print(f"成功加载模型: {main_model_path.name}")

    # 初始化结果变量
    cv_df = None
    comparison_df = None
    error_analysis = None
    cm = None
    classification_report_dict = None

    try:
        # 1. 交叉验证
        if not args.skip_cv:
            cv_df = perform_cross_validation(main_model_path, CONFIG_DIR / 'data.yaml', data_config, args.cv_folds)

        # 2. 模型对比
        if not args.skip_comparison and len(model_files) > 1:
            comparison_df = compare_models(model_files, CONFIG_DIR / 'data.yaml', data_config)

        # 3. 错误分析
        if not args.skip_error:
            error_analysis = analyze_prediction_errors(main_model, data_config, args.error_samples)

        # 4. 混淆矩阵
        cm_result = generate_confusion_matrix(main_model, data_config, class_names, num_classes)
        if cm_result:
            cm, classification_report_dict = cm_result

        # 5. 可视化结果
        visualize_results(cv_df, comparison_df, error_analysis, cm, RESULTS_DIR)

        # 6. 生成综合报告
        final_report = generate_comprehensive_report(
            main_model_path, data_config, class_names, num_classes,
            cv_df, comparison_df, error_analysis, classification_report_dict, RESULTS_DIR
        )

        # 7. 打印总结
        print(f"\n{'='*80}")
        print("模型测试与评估分析完成")
        print(f"{'='*80}")

        print("\n📊 本次分析包含以下内容:")
        print("1. ✅ 交叉验证 - 评估模型稳定性和泛化能力")
        print("2. ✅ 性能对比 - 与其他模型版本比较")
        print("3. ✅ 错误分析 - 识别模型预测错误的案例")
        print("4. ✅ 混淆矩阵 - 详细的分类性能分析")
        print("5. ✅ 综合报告 - 完整的评估结果文档")

        print("\n📁 生成的文件:")
        result_files = list(RESULTS_DIR.glob('*'))
        for file in result_files:
            print(f"   - {file.name}")

        print("\n💡 基于分析结果的建议:")
        if cv_df is not None:
            cv_std = cv_df['mAP50'].std()
            if cv_std > 0.05:
                print("   - 模型稳定性有待提高，建议增加训练数据或调整超参数")
            else:
                print("   - 模型表现稳定，泛化能力良好")

        if error_analysis:
            fp_count = len(error_analysis['false_positives'])
            fn_count = len(error_analysis['false_negatives'])

            if fp_count > fn_count:
                print("   - 假阳性较多，建议提高置信度阈值或改进后处理")
            elif fn_count > fp_count:
                print("   - 假阴性较多，建议降低置信度阈值或增强数据增强")
            else:
                print("   - 假阳性和假阴性平衡，模型性能良好")

        print("\n🎯 报告使用建议:")
        print("   - 将生成的图表和数据用于学术报告的4.2节")
        print("   - 重点关注交叉验证结果展示模型稳定性")
        print("   - 使用性能对比结果证明模型优势")
        print("   - 错误分析结果可用于讨论模型改进方向")

        print(f"\n{'='*80}")

    except Exception as e:
        print(f"评估过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
