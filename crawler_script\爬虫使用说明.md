# 口罩图片爬虫使用说明

## 功能介绍
这个Python爬虫脚本可以自动爬取三种类型的口罩相关图片：
- 戴口罩的人 (with_mask)
- 未戴口罩的人 (without_mask)
- 不规范戴口罩的人 (incorrect_mask)

## 环境要求
- Python 3.7+
- FMD虚拟环境

## 项目集成特点
- **路径管理集成**: 使用项目中`yoloserver/utils/paths.py`定义的路径常量
- **自动目录创建**: 通过`initialize_project.py`自动创建必要的目录结构
- **统一项目结构**: 完全集成到现有的FaceMaskDetection项目中

## 使用方法

### 方法1：使用批处理脚本（推荐）
```bash
# 直接运行批处理脚本（会自动处理所有步骤）
run_crawler.bat
```

### 方法2：手动运行
```bash
# 1. 激活FMD虚拟环境
conda activate FMD

# 2. 切换到项目根目录并初始化项目结构
python yoloserver\initialize_project.py

# 3. 切换到爬虫脚本目录
cd crawl_script

# 4. 安装依赖包
pip install -r crawler_requirements.txt

# 5. 运行爬虫（完整版）
python mask_image_crawler.py

# 或运行测试版（少量图片）
python test_crawler.py
```

## 输出结构
爬取的图片将自动保存到项目定义的目录结构中：
```
yoloserver/data/crawled/
├── images/                    # 所有爬取的图片（按类型前缀命名）
│   ├── with_mask_xxxxx.jpg    # 戴口罩的图片
│   ├── without_mask_xxxxx.jpg # 未戴口罩的图片
│   └── incorrect_mask_xxxxx.jpg # 不规范戴口罩的图片
└── original_annotations/      # 标注文件目录（预留）
```

- 路径由`yoloserver/utils/paths.py`中的`CRAWLED_IMAGES_DIR`和`CRAWLED_ORIGINAL_ANNOTATIONS_DIR`常量定义
- 每个目录都包含一个`.keep`文件用于git版本控制
- 图片文件名包含MD5哈希值确保唯一性

## 功能特点
- **项目集成**: 完全集成到现有项目的路径管理系统
- **自动去重**: 使用MD5哈希值避免下载重复图片
- **智能过滤**: 自动跳过过小或非图片文件
- **多关键词搜索**: 每个分类使用多个搜索关键词提高覆盖率
- **随机延时**: 避免请求过快被封禁
- **错误处理**: 完善的异常处理机制
- **进度显示**: 实时显示下载进度

## 配置选项
可以在`main()`函数中修改以下参数：
- `images_per_category`: 每个分类下载的图片数量（默认30张）

可以在`crawl_all_categories()`方法中修改：
- `search_queries`: 搜索关键词列表

## 注意事项
1. 请确保网络连接稳定
2. 爬取过程中会有随机延时，请耐心等待
3. 如果遇到网络错误，脚本会自动跳过并继续
4. 建议在非高峰时段运行以获得更好的效果
5. 请遵守网站的robots.txt和使用条款

## 故障排除
- 如果下载失败率较高，可能是网络问题，建议稍后重试
- 如果某些图片无法下载，脚本会自动跳过并继续
- 确保有足够的磁盘空间存储图片

## 扩展功能
如需添加更多搜索关键词或修改搜索逻辑，可以编辑`crawl_all_categories()`方法中的`search_queries`字典。
