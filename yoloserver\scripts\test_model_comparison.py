#!/usr/bin/env python
# -*- coding:utf-8 -*-
# @FileName  :test_model_comparison.py
# @Time      :2025/7/29
# <AUTHOR> Assistant
# @Project   :FaceMaskDetection
# @Function  :测试模型性能对比功能

import sys
from pathlib import Path

# 添加项目路径
current_path = Path(__file__).parent.parent.resolve()
utils_path = current_path / 'utils'
if str(current_path) not in sys.path:
    sys.path.insert(0, str(current_path))
if str(utils_path) not in sys.path:
    sys.path.insert(1, str(utils_path))

from model_evaluation import (
    setup_paths, load_config, get_best_model, 
    compare_models, visualize_results
)

def test_model_comparison():
    """测试模型性能对比功能"""
    print("="*60)
    print("测试模型性能对比功能")
    print("="*60)
    
    try:
        # 设置路径
        CONFIG_DIR, MODEL_DIR, DATA_DIR, RESULTS_DIR = setup_paths()
        
        # 加载配置
        data_config, class_names, num_classes = load_config(CONFIG_DIR)
        
        # 获取模型
        main_model_path, model_files = get_best_model(MODEL_DIR)
        
        if not main_model_path:
            print("❌ 未找到可用的模型文件")
            return False
        
        print(f"✅ 找到 {len(model_files)} 个模型文件")
        for i, model_file in enumerate(model_files):
            print(f"  {i+1}. {model_file.name}")
        
        # 测试模型对比
        if len(model_files) >= 1:
            print(f"\n开始测试模型性能对比...")
            comparison_df = compare_models(model_files, CONFIG_DIR / 'data.yaml', data_config)
            
            if comparison_df is not None:
                print(f"✅ 模型对比成功，共评估 {len(comparison_df)} 个模型")
                
                # 测试可视化
                print(f"\n开始测试可视化功能...")
                visualize_results(None, comparison_df, None, None, RESULTS_DIR)
                print(f"✅ 可视化完成")
                
                return True
            else:
                print("❌ 模型对比失败")
                return False
        else:
            print("⚠️  没有足够的模型文件进行对比测试")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_model_comparison()
    if success:
        print(f"\n{'='*60}")
        print("✅ 模型性能对比功能测试通过")
        print(f"{'='*60}")
    else:
        print(f"\n{'='*60}")
        print("❌ 模型性能对比功能测试失败")
        print(f"{'='*60}")
