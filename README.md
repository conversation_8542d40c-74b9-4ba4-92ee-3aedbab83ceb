# FaceMaskDetection - 智能口罩检测系统

基于YOLO12深度学习的智能口罩佩戴检测平台，集成Django Web前端、YOLO推理服务、大模型AI分析和数据爬虫等完整功能模块。

## 项目概述

FaceMaskDetection是一个完整的口罩检测解决方案，专注于检测图像中的人员是否正确佩戴口罩。系统支持三种检测类别：
- **正确戴口罩** (with_mask) - 绿色标注
- **未戴口罩** (without_mask) - 红色标注
- **错误戴口罩** (mask_weared_incorrect) - 黄色标注

### 核心特性
- **Web界面**: 现代化的Django前端，支持拖拽上传和实时检测
- **AI分析**: 集成SiliconFlow API，支持Qwen、DeepSeek等大模型进行智能分析
- **数据管理**: 完整的检测历史记录和统计分析
- **用户系统**: 多用户认证和权限管理
- **数据采集**: 自动化图片爬虫系统
- **企业级安全**: 完善的安全防护和权限控制

## 功能特性

### AI检测功能
- **YOLO12模型**: 最新的目标检测算法，支持GPU/CPU自动切换
- **实时处理**: 快速图像上传和检测，支持多种格式（JPG、PNG、BMP）
- **结果美化**: 高质量的检测结果可视化，支持中文标签和圆角边框
- **批量处理**: 支持单张和多张图片同时检测
- **参数调节**: 可调整置信度阈值、IOU阈值、图像尺寸等参数

### 智能分析
- **大模型集成**: 支持多种AI模型（Qwen QwQ-32B、Qwen 2.5系列、DeepSeek V2.5）
- **专业分析**: AI生成的检测结果分析报告，包含合规性评估和改进建议
- **流式输出**: 实时显示分析过程，提升用户体验
- **PDF导出**: 支持将分析结果导出为PDF格式
- **自定义提示**: 支持用户自定义分析提示词

### 数据管理
- **检测历史**: 完整的检测记录管理，支持搜索和筛选
- **统计分析**: 详细的检测数据统计和可视化
- **数据导出**: 支持检测结果和分析报告导出
- **用户隔离**: 安全的多用户数据管理
- **模型管理**: 自动发现和管理YOLO模型文件

### 现代化界面
- **响应式设计**: 支持桌面和移动设备
- **Bootstrap 5**: 现代化的UI组件和交互
- **拖拽上传**: 便捷的图片上传体验
- **实时反馈**: 动态的处理状态显示
- **美观设计**: 渐变效果和现代化视觉设计

### 用户认证系统
- **多用户支持**: 完整的用户注册、登录、权限管理
- **数据隔离**: 每个用户只能查看和管理自己的检测记录
- **权限分级**: 管理员可查看全站数据，普通用户仅限个人数据
- **安全防护**: 登录限制、CSRF保护、XSS防护、SQL注入防护

### 企业级安全
- **登录保护**: 5次失败后IP锁定30分钟
- **会话管理**: 安全的会话控制和自动过期
- **审计日志**: 完整的登录记录和操作追踪
- **密码安全**: Django内置哈希加密存储
- **文件验证**: 严格的文件类型和大小限制

## 快速开始

### 环境要求
- **Python**: 3.8+
- **操作系统**: Windows/Linux/macOS
- **内存**: 建议8GB以上
- **存储**: 至少2GB可用空间
- **网络**: 用于大模型API调用和数据爬虫

### 1. 环境准备
```bash
# 创建虚拟环境
conda create -n FMD python=3.8+
conda activate FMD

# 克隆项目
git clone <repository-url>
cd FaceMaskDetection
```

### 2. 一键启动（推荐）
```bash
# 进入Django前端目录
cd django_frontend

# 运行启动脚本（自动安装依赖、初始化项目、启动服务器）
python start_server.py
```

### 3. 手动安装步骤
```bash
# 1. 安装Django前端依赖
cd django_frontend
pip install -r requirements.txt

# 2. 初始化项目结构
cd ../yoloserver
python initialize_project.py

# 3. 设置数据库
cd ../django_frontend
python manage.py makemigrations
python manage.py migrate

# 4. 设置用户认证系统（创建管理员账户）
python manage.py setup_auth

# 5. 收集静态文件
python manage.py collectstatic

# 6. 启动服务器
python manage.py runserver
```

### 4. 访问系统
- **系统入口**: http://127.0.0.1:8000
- **默认管理员**: admin / admin123
- **管理后台**: http://127.0.0.1:8000/admin/
- **检测页面**: http://127.0.0.1:8000/detect/
- **历史记录**: http://127.0.0.1:8000/history/

## 使用指南

### 图片检测流程
1. **访问主页** http://127.0.0.1:8000
2. **上传图片**: 拖拽或点击上传图片（支持JPG、PNG、BMP格式，最大10MB）
3. **调整参数**（可选）：
   - **模型选择**: 从可用的YOLO模型中选择
   - **置信度阈值**: 0.1-1.0范围，越高越严格
   - **IOU阈值**: 重叠度控制，0.1-1.0范围
   - **图像尺寸**: 320/640/1280像素，越大精度越高
4. **开始检测**: 点击"开始检测"按钮
5. **查看结果**: 等待AI处理完成，查看检测结果和统计信息

### AI智能分析功能
1. **在检测结果页面**，找到"AI智能分析"模块
2. **输入分析提示词**：
   - 默认提示：基于检测结果分析口罩佩戴情况
   - 自定义提示：如"给出改进建议"、"评估合规性"等
3. **选择AI模型**：
   - Qwen QwQ-32B（强大的推理模型，适合复杂分析）
   - Qwen 2.5-7B-Instruct（快速响应，适合一般分析）
   - Qwen 2.5-14B-Instruct（平衡性能，综合分析能力强）
   - DeepSeek V2.5（深度思考模型，适合专业分析）
4. **开始分析**: 点击"开始AI分析"按钮
5. **查看分析结果**: 获取专业的智能分析报告
6. **操作结果**: 复制结果或下载PDF分析报告

### 历史记录管理
1. **访问历史页面**: 点击导航栏"检测历史"
2. **浏览记录**: 查看所有检测记录的卡片式展示
3. **搜索筛选**: 按状态、时间范围搜索
4. **查看详情**: 点击"查看详情"查看完整结果
5. **删除记录**: 删除不需要的检测记录

### 模型管理
1. **添加模型**: 将训练好的.pt模型文件放入 `yoloserver/models/checkpoints/` 目录
2. **自动发现**: 系统会自动扫描目录下的模型文件
3. **查看状态**: 访问"模型管理"页面查看所有模型状态
4. **配置模型**: 在管理后台为模型添加详细描述、准确率、推理速度等信息

## 项目结构

```
FaceMaskDetection/
├── README.md                           # 项目主文档（本文件）
├── simhei.ttf                          # 中文字体文件
│
├── django_frontend/                    # Django Web前端
│   ├── face_mask_detection/           # Django项目配置
│   │   ├── settings.py               # 项目设置
│   │   ├── urls.py                   # 主URL配置
│   │   ├── wsgi.py                   # WSGI配置
│   │   └── asgi.py                   # ASGI配置
│   ├── detection/                     # 主应用模块
│   │   ├── models.py                 # 数据模型（用户、检测记录）
│   │   ├── views.py                  # 视图函数
│   │   ├── api_views.py              # API接口（含大模型API）
│   │   ├── services.py               # YOLO推理服务
│   │   ├── forms.py                  # 表单定义
│   │   ├── urls.py                   # 应用URL
│   │   ├── admin.py                  # 管理后台
│   │   └── management/               # 管理命令
│   │       └── commands/
│   │           └── setup_auth.py     # 用户认证设置命令
│   ├── templates/                     # HTML模板
│   │   ├── base.html                 # 基础模板
│   │   ├── registration/             # 用户认证模板
│   │   └── detection/                # 检测相关模板
│   │       ├── index.html            # 主页
│   │       ├── result.html           # 结果页（含AI分析）
│   │       ├── history.html          # 历史记录
│   │       ├── models.html           # 模型管理
│   │       └── settings.html         # 系统设置
│   ├── static/                        # 静态文件
│   │   ├── css/custom.css            # 自定义样式
│   │   └── js/detection.js           # 前端JavaScript
│   ├── media/                         # 媒体文件目录
│   │   └── uploads/                  # 上传文件存储
│   ├── logs/                          # 日志文件
│   ├── manage.py                      # Django管理脚本
│   ├── requirements.txt               # Python依赖
│   ├── setup_django.py               # 项目初始化脚本
│   ├── start_server.py               # 服务器启动脚本
│   ├── llm_config.py                 # 大模型配置
│   └── README.md                     # Django前端详细文档
│
├── yoloserver/                        # YOLO训练和推理服务
│   ├── configs/                      # 配置文件目录
│   │   ├── data.yaml                # 数据集配置
│   │   ├── train.yaml               # 训练配置
│   │   └── infer.yaml               # 推理配置
│   ├── data/                         # 数据目录
│   │   ├── train/                   # 训练数据
│   │   ├── val/                     # 验证数据
│   │   ├── test/                    # 测试数据
│   │   ├── raw/                     # 原始数据
│   │   └── crawled/                 # 爬虫数据
│   ├── models/                       # 模型目录
│   │   ├── checkpoints/             # 训练好的模型
│   │   └── pretrained/              # 预训练模型
│   ├── runs/                         # 运行结果
│   │   ├── train/                   # 训练结果
│   │   ├── val/                     # 验证结果
│   │   └── detect/                  # 检测结果
│   ├── logs/                         # 日志文件
│   ├── scripts/                      # 核心脚本
│   │   ├── yolo_train.py            # 模型训练
│   │   ├── yolo_val.py              # 模型验证
│   │   ├── yolo_infer.py            # 模型推理
│   │   └── yolo_trans.py            # 数据转换
│   ├── utils/                        # 工具模块
│   │   ├── paths.py                 # 路径配置
│   │   ├── config_utils.py          # 配置管理
│   │   ├── data_utils.py            # 数据处理
│   │   ├── logging_utils.py         # 日志管理
│   │   ├── beautify.py              # 结果美化
│   │   ├── system_utils.py          # 系统工具
│   │   └── data_converters/         # 数据格式转换
│   ├── initialize_project.py         # 项目初始化
│   └── README.md                     # YOLO服务详细文档
│
└── crawler_script/                    # 数据爬虫模块
    ├── mask_image_crawler.py         # 主爬虫脚本
    ├── test_crawler.py               # 测试爬虫脚本
    ├── run_crawler.bat               # Windows批处理启动脚本
    ├── crawler_requirements.txt      # 爬虫依赖
    └── 爬虫使用说明.md                # 爬虫使用文档
```

### 核心组件说明

#### Django前端 (django_frontend/)
- **Web界面**: 基于Django 4.2+的现代化Web应用
- **用户系统**: 完整的用户认证和权限管理
- **检测服务**: 集成YOLO推理和大模型分析
- **数据管理**: 检测历史记录和统计分析

#### YOLO服务 (yoloserver/)
- **模型训练**: 支持自定义数据集训练YOLO模型
- **模型推理**: 高性能的图像检测推理服务
- **数据处理**: 完整的数据预处理和格式转换
- **结果美化**: 支持中文标签和高质量可视化

#### 数据爬虫 (crawler_script/)
- **自动采集**: 多关键词搜索的图片爬虫
- **智能去重**: 基于MD5哈希的重复检测
- **分类存储**: 按检测类别自动分类存储

## 技术栈与依赖

### 后端技术
- **Django 4.2+**: Web框架，提供完整的MVC架构
- **YOLO12**: 最新的深度学习目标检测模型
- **Ultralytics**: YOLO框架实现，支持训练和推理
- **SQLite/PostgreSQL**: 数据库，支持升级到生产环境
- **Pillow**: 图像处理库，支持多种格式
- **OpenCV**: 计算机视觉库，用于图像预处理

### 前端技术
- **Bootstrap 5**: 响应式CSS框架
- **JavaScript/jQuery**: 前端交互逻辑
- **Font Awesome**: 图标库
- **自定义CSS**: 美化界面和用户体验

### AI技术集成
- **SiliconFlow API**: 主要使用的大模型API服务
- **Qwen系列模型**: QwQ-32B、Qwen 2.5-7B/14B-Instruct
- **DeepSeek V2.5**: 深度思考模型
- **ReportLab**: PDF生成库

### 开发工具
- **Python 3.8+**: 编程语言
- **Conda**: 虚拟环境管理
- **Django Admin**: 管理后台
- **Django ORM**: 数据库操作
- **Django Templates**: 模板引擎

### 主要依赖包

#### Django前端依赖 (django_frontend/requirements.txt)
```
Django>=4.2.0
Pillow>=9.0.0
django-cors-headers>=4.0.0
ultralytics>=8.0.0
opencv-python>=4.7.0
numpy>=1.21.0
PyYAML>=6.0
reportlab>=4.0.0
```

#### 爬虫依赖 (crawler_script/crawler_requirements.txt)
```
requests>=2.25.1
beautifulsoup4>=4.9.3
lxml>=4.6.3
Pillow>=8.2.0
pathlib2>=2.3.6
```

## 配置说明

### 核心配置文件

#### Django配置 (django_frontend/face_mask_detection/settings.py)
```python
# YOLO服务器路径配置
YOLO_SERVER_ROOT = BASE_DIR.parent / 'yoloserver'
YOLO_MODELS_DIR = YOLO_SERVER_ROOT / 'models' / 'checkpoints'
YOLO_SCRIPTS_DIR = YOLO_SERVER_ROOT / 'scripts'

# 文件上传限制
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10MB
ALLOWED_IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.bmp']

# 数据库配置
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = BASE_DIR / 'staticfiles'
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'
```

#### YOLO数据配置 (yoloserver/configs/data.yaml)
```yaml
path: C:\FaceMaskDetection\yoloserver\data
train: C:\FaceMaskDetection\yoloserver\data\train\images
val: C:\FaceMaskDetection\yoloserver\data\val\images
test: C:\FaceMaskDetection\yoloserver\data\test\images
nc: 3
names: [mask_weared_incorrect, with_mask, without_mask]
```

#### 大模型配置 (django_frontend/llm_config.py)
```python
# SiliconFlow API配置
SILICONFLOW_CONFIG = {
    'api_key': os.getenv('SILICONFLOW_API_KEY', 'sk-your-api-key-here'),
    'base_url': 'https://api.siliconflow.cn/v1/chat/completions',
    'timeout': 120,
    'max_tokens': 4000,
    'temperature': 0.7
}

# 可用模型列表
AVAILABLE_MODELS = [
    'Qwen/QwQ-32B',
    'Qwen/Qwen2.5-7B-Instruct',
    'Qwen/Qwen2.5-14B-Instruct',
    'deepseek-ai/DeepSeek-V2.5'
]

# 默认模型
DEFAULT_MODEL = 'Qwen/QwQ-32B'
```

### 数据库设计

#### 主要数据模型
```python
# 用户配置模型
class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    login_count = models.IntegerField(default=0)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    is_locked = models.BooleanField(default=False)
    locked_until = models.DateTimeField(null=True, blank=True)

# 检测记录模型
class DetectionRecord(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    upload_time = models.DateTimeField(auto_now_add=True)
    original_image = models.ImageField(upload_to='uploads/original/')
    result_image = models.ImageField(upload_to='uploads/results/')

    # 检测参数
    model_name = models.CharField(max_length=100)
    confidence_threshold = models.FloatField(default=0.25)
    iou_threshold = models.FloatField(default=0.45)
    image_size = models.IntegerField(default=640)

    # 检测结果
    total_detections = models.IntegerField(default=0)
    with_mask_count = models.IntegerField(default=0)
    without_mask_count = models.IntegerField(default=0)
    incorrect_mask_count = models.IntegerField(default=0)

    # 详细数据
    detection_details = models.JSONField(default=dict)
    processing_time = models.FloatField(null=True)
    status = models.CharField(max_length=20, default='completed')
```

## API接口文档

### RESTful API端点
| 端点 | 方法 | 功能说明 |
|------|------|----------|
| `/api/detect/` | POST | 上传图片并检测 |
| `/api/result/<id>/` | GET | 获取检测结果 |
| `/api/models/` | GET | 获取可用模型列表 |
| `/api/history/` | GET | 获取检测历史 |
| `/api/delete/<id>/` | DELETE | 删除检测记录 |
| `/api/llm-analysis/` | POST | 大模型分析接口 |

### API使用示例

#### 图片检测
```javascript
const formData = new FormData();
formData.append('image', imageFile);
formData.append('model_name', 'YOLO12n-seg.pt');
formData.append('confidence', 0.25);
formData.append('iou', 0.45);
formData.append('imgsz', 640);

fetch('/api/detect/', {
    method: 'POST',
    body: formData
})
.then(response => response.json())
.then(data => console.log('检测结果:', data));
```

#### 大模型分析
```javascript
const analysisData = {
    prompt: "请分析这张图片中的口罩佩戴情况",
    model: "Qwen/QwQ-32B",
    record_id: 17,
    detection_data: {
        total_detections: 5,
        with_mask_count: 3,
        without_mask_count: 1,
        incorrect_mask_count: 1
    }
};

fetch('/api/llm-analysis/', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
        'X-CSRFToken': getCookie('csrftoken')
    },
    body: JSON.stringify(analysisData)
});
```

## YOLO服务使用指南

### 模型训练
```bash
# 基础训练
python scripts/yolo_train.py --data data.yaml --epochs 200 --batch 16

# 使用预训练模型
python scripts/yolo_train.py --weights YOLO12n-seg.pt --data data.yaml --epochs 200

# 使用YAML配置文件
python scripts/yolo_train.py --use_yaml True
```

### 模型验证
```bash
# 验证训练好的模型
python scripts/yolo_val.py --weights best.pt --data data.yaml

# 在测试集上验证
python scripts/yolo_val.py --weights best.pt --split test
```

### 模型推理
```bash
# 图像推理
python scripts/yolo_infer.py --weights best.pt --source image.jpg

# 视频推理
python scripts/yolo_infer.py --weights best.pt --source video.mp4

# 摄像头实时推理
python scripts/yolo_infer.py --weights best.pt --source 0

# 批量推理
python scripts/yolo_infer.py --weights best.pt --source images_folder/
```

### 数据转换
```bash
# 转换COCO格式数据
python scripts/yolo_trans.py --format coco --input data/raw/original_annotations/

# 转换Pascal VOC格式数据
python scripts/yolo_trans.py --format pascal_voc --input data/raw/original_annotations/
```

## 数据爬虫使用指南

### 快速启动
```bash
# 方法1：使用批处理脚本（推荐）
run_crawler.bat

# 方法2：手动运行
conda activate FMD
cd crawler_script
pip install -r crawler_requirements.txt
python mask_image_crawler.py
```

### 输出结构
爬取的图片将自动保存到项目定义的目录结构中：
```
yoloserver/data/crawled/
├── images/                    # 所有爬取的图片（按类型前缀命名）
│   ├── with_mask_xxxxx.jpg    # 戴口罩的图片
│   ├── without_mask_xxxxx.jpg # 未戴口罩的图片
│   └── incorrect_mask_xxxxx.jpg # 不规范戴口罩的图片
└── original_annotations/      # 标注文件目录（预留）
```

### 功能特点
- **项目集成**: 完全集成到现有项目的路径管理系统
- **自动去重**: 使用MD5哈希值避免下载重复图片
- **智能过滤**: 自动跳过过小或非图片文件
- **多关键词搜索**: 每个分类使用多个搜索关键词提高覆盖率
- **随机延时**: 避免请求过快被封禁
- **错误处理**: 完善的异常处理机制

## 安全与性能

### 安全措施
- **CSRF保护**: Django内置CSRF防护，所有表单和API请求都受保护
- **文件验证**: 严格的文件类型和大小限制
- **路径安全**: 防止路径遍历攻击
- **输入验证**: 所有用户输入都经过严格验证
- **权限控制**: 基于Django的权限系统
- **SQL注入防护**: 使用Django ORM防止SQL注入

### 性能优化
- **异步处理**: 避免页面阻塞，提升用户体验
- **文件管理**: 自动清理临时文件，节省存储空间
- **数据库优化**: 高效的查询设计，合理的索引配置
- **静态文件**: CDN就绪的静态资源管理
- **缓存机制**: Session缓存用户设置，减少重复计算
- **图片处理**: 智能图片压缩和格式转换

## 故障排除

### 常见问题及解决方案

#### 1. YOLO推理失败
**问题现象**: 检测失败，显示推理错误
**解决方案**:
- 检查 `yoloserver` 目录是否存在
- 确认模型文件在 `yoloserver/models/checkpoints/` 目录下
- 检查模型文件是否为有效的 `.pt` 文件
- 查看Django终端日志获取详细错误信息
- 确认Python环境中已安装ultralytics包

#### 2. 图片上传失败
**问题现象**: 上传按钮无响应或显示上传错误
**解决方案**:
- 检查文件大小是否超过10MB限制
- 确认文件格式是否为支持的格式（JPG、PNG、BMP）
- 检查 `media` 目录是否存在且有写入权限
- 确认Django设置中的MEDIA_ROOT配置正确

#### 3. 大模型API调用失败
**问题现象**: AI分析功能无响应或返回错误
**解决方案**:
- 检查网络连接是否正常
- 确认API密钥配置正确
- 检查请求数据格式是否正确
- 查看Django日志中的详细错误信息

#### 4. 数据库相关问题
**问题现象**: 数据库操作失败或数据丢失
**解决方案**:
```bash
# 重新创建迁移文件
python manage.py makemigrations

# 应用迁移
python manage.py migrate

# 如果数据库损坏，删除db.sqlite3重新初始化
rm db.sqlite3
python manage.py migrate
python manage.py createsuperuser
```

## 扩展建议

### 短期扩展
1. **批量检测**: 支持多张图片同时上传检测
2. **数据导出**: Excel/CSV格式导出检测结果
3. **检测报告**: 自动生成PDF格式的检测报告
4. **API认证**: Token认证机制，支持第三方集成
5. **缓存优化**: Redis缓存支持，提升性能

### 中期规划
1. **实时检测**: 摄像头实时检测功能
2. **移动端APP**: React Native或Flutter移动应用
3. **异步处理**: Celery任务队列，处理大量并发
4. **监控系统**: 添加日志和性能监控
5. **多语言支持**: 国际化和本地化

### 长期愿景
1. **云端部署**: AWS/Azure/阿里云部署方案
2. **微服务架构**: 拆分为多个独立服务
3. **大数据分析**: 检测数据的深度分析和可视化
4. **AI模型训练**: 在线模型训练和优化平台
5. **企业级集成**: 与现有企业系统集成

## 部署指南

### 开发环境
```bash
# 使用Django开发服务器
python manage.py runserver
```

### 生产环境
```bash
# 使用Gunicorn + Nginx
pip install gunicorn
gunicorn face_mask_detection.wsgi:application --bind 0.0.0.0:8000

# 配置Nginx反向代理
# 升级数据库到PostgreSQL
# 配置静态文件CDN
# 设置SSL证书
```

## 版本历史

### v2.1.0 (2025-07-03)
- 新增完整的项目文档整合
- 优化README结构和内容组织
- 减少emoji使用，提升专业性
- 整合所有子模块文档内容

### v2.0.0 (2025-07-01)
- 新增完整的用户认证和权限管理系统
- 实现多用户数据隔离
- 添加企业级安全防护措施
- 优化用户界面和交互体验

### v1.0.0 (2025-06-29)
- 基础的YOLO口罩检测功能
- Django Web界面
- 大模型分析集成
- 检测历史管理

## 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发流程
1. Fork项目到个人仓库
2. 创建功能分支
3. 提交代码并编写测试
4. 创建Pull Request
5. 代码审查和合并

### 代码规范
- 遵循PEP 8 Python代码规范
- 添加适当的注释和文档字符串
- 编写单元测试覆盖新功能
- 更新相关文档

## 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请：
1. 查看本文档的故障排除部分
2. 检查Django终端日志信息
3. 提交GitHub Issue
4. 联系开发团队获取技术支持

---

**项目状态**: 生产就绪
**技术支持**: 完整的文档和测试覆盖
**安全等级**: 企业级安全防护
**用户支持**: 多用户权限管理
**最后更新**: 2025-07-03