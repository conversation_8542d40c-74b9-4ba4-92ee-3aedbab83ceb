{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-images"></i> {{ page_title }}
            </h2>
            <div>
                <a href="{% url 'batch_history' %}" class="btn btn-outline-secondary me-2">
                    <i class="fas fa-list"></i> 批量历史
                </a>
                <a href="{% url 'detect' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新检测
                </a>
            </div>
        </div>
        
        <!-- 会话信息卡片 -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle"></i> 会话信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <p><strong>会话名称:</strong> {{ session.session_name }}</p>
                        <p><strong>创建时间:</strong> {{ session.created_time|date:"Y-m-d H:i:s" }}</p>
                        <p><strong>检测模型:</strong> {{ session.model_name }}</p>
                        <p><strong>置信度阈值:</strong> {{ session.confidence_threshold }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>状态:</strong> 
                            <span class="badge 
                                {% if session.status == 'completed' %}bg-success
                                {% elif session.status == 'partial_completed' %}bg-warning
                                {% elif session.status == 'failed' %}bg-danger
                                {% elif session.status == 'processing' %}bg-info
                                {% else %}bg-secondary{% endif %}">
                                {{ session.get_status_display }}
                            </span>
                        </p>
                        <p><strong>IOU阈值:</strong> {{ session.iou_threshold }}</p>
                        <p><strong>图像尺寸:</strong> {{ session.image_size }}x{{ session.image_size }}</p>
                        {% if session.total_processing_time %}
                        <p><strong>总处理时间:</strong> {{ session.total_processing_time|floatformat:2 }}秒</p>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 统计概览 -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ batch_stats.total_images }}</h3>
                        <p class="card-text">总图片数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ batch_stats.completed_images }}</h3>
                        <p class="card-text">成功处理</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-danger">{{ batch_stats.failed_images }}</h3>
                        <p class="card-text">处理失败</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-info">{{ batch_stats.success_rate }}%</h3>
                        <p class="card-text">成功率</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 检测结果统计 -->
        {% if batch_stats.total_detections > 0 %}
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-primary">{{ batch_stats.total_detections }}</h3>
                        <p class="card-text">总检测数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-success">{{ batch_stats.total_with_mask }}</h3>
                        <p class="card-text">正确戴口罩</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-danger">{{ batch_stats.total_without_mask }}</h3>
                        <p class="card-text">未戴口罩</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h3 class="text-warning">{{ batch_stats.total_incorrect_mask }}</h3>
                        <p class="card-text">错误戴口罩</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 合规率进度条 -->
        <div class="card mb-4">
            <div class="card-body">
                <h6 class="card-title">整体口罩合规率</h6>
                <div class="progress">
                    <div class="progress-bar bg-success" 
                         style="width: {{ batch_stats.overall_compliance_rate }}%"></div>
                </div>
                <div class="text-center mt-2">
                    <strong>{{ batch_stats.overall_compliance_rate }}%</strong>
                </div>
            </div>
        </div>
        {% endif %}
        
        <!-- 检测结果列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 检测结果详情
                </h5>
            </div>
            <div class="card-body">
                {% if page_obj %}
                <div class="row">
                    {% for record in page_obj %}
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="card detection-card h-100">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <small class="text-muted">#{{ record.batch_index|add:1 }}</small>
                                <span class="badge status-badge
                                    {% if record.status == 'completed' %}bg-success
                                    {% elif record.status == 'failed' %}bg-danger
                                    {% elif record.status == 'processing' %}bg-warning
                                    {% else %}bg-secondary{% endif %}">
                                    {% if record.status == 'completed' %}已完成
                                    {% elif record.status == 'failed' %}失败
                                    {% elif record.status == 'processing' %}处理中
                                    {% else %}等待中{% endif %}
                                </span>
                            </div>
                            
                            <div class="card-body">
                                <!-- 图片预览 -->
                                <div class="text-center mb-3">
                                    {% if record.result_image %}
                                        <img src="{{ record.result_image.url }}" 
                                             class="img-fluid rounded" 
                                             style="max-height: 150px; object-fit: cover;"
                                             alt="检测结果">
                                    {% elif record.original_image %}
                                        <img src="{{ record.original_image.url }}" 
                                             class="img-fluid rounded" 
                                             style="max-height: 150px; object-fit: cover;"
                                             alt="原始图片">
                                    {% else %}
                                        <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                             style="height: 150px;">
                                            <i class="fas fa-image fa-2x text-muted"></i>
                                        </div>
                                    {% endif %}
                                </div>
                                
                                <!-- 文件信息 -->
                                <div class="mb-2">
                                    <strong>文件名:</strong> 
                                    <small>{{ record.filename }}</small>
                                </div>
                                
                                {% if record.status == 'completed' %}
                                <!-- 检测结果 -->
                                <div class="mb-2">
                                    <strong>检测结果:</strong> {{ record.total_detections }} 个
                                </div>
                                
                                {% if record.total_detections > 0 %}
                                <div class="row text-center">
                                    <div class="col-4">
                                        <small class="text-success">✓ {{ record.with_mask_count }}</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-danger">✗ {{ record.without_mask_count }}</small>
                                    </div>
                                    <div class="col-4">
                                        <small class="text-warning">⚠ {{ record.incorrect_mask_count }}</small>
                                    </div>
                                </div>
                                
                                <div class="progress mt-2">
                                    <div class="progress-bar bg-success" 
                                         style="width: {{ record.detection_summary.compliance_rate }}%"></div>
                                </div>
                                <div class="text-center mt-1">
                                    <small>合规率: {{ record.detection_summary.compliance_rate }}%</small>
                                </div>
                                {% endif %}
                                
                                {% if record.processing_time %}
                                <div class="mt-2">
                                    <small class="text-muted">处理时间: {{ record.processing_time|floatformat:2 }}秒</small>
                                </div>
                                {% endif %}
                                {% elif record.status == 'failed' %}
                                <div class="text-danger">
                                    <small>{{ record.error_message|default:"处理失败" }}</small>
                                </div>
                                {% endif %}
                            </div>
                            
                            {% if record.status == 'completed' %}
                            <div class="card-footer">
                                <a href="{% url 'detection_result' record.id %}" 
                                   class="btn btn-sm btn-primary w-100">
                                    <i class="fas fa-eye"></i> 查看详情
                                </a>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <!-- 分页 -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="检测结果分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-images fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无检测结果</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
