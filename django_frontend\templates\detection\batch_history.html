{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题和操作 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-history"></i> {{ page_title }}
            </h2>
            <div>
                <a href="{% url 'detect' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 新检测
                </a>
            </div>
        </div>
        
        <!-- 搜索和筛选 -->
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    {% csrf_token %}
                    <div class="col-md-4">
                        <label for="search" class="form-label">搜索</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="会话名称或ID">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">全部状态</option>
                            {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                {{ label }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    {% if is_superuser %}
                    <div class="col-md-3">
                        <label for="show_all" class="form-label">数据范围</label>
                        <select class="form-select" id="show_all" name="show_all">
                            <option value="false" {% if not show_all %}selected{% endif %}>我的数据</option>
                            <option value="true" {% if show_all %}selected{% endif %}>全站数据</option>
                        </select>
                    </div>
                    {% endif %}
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 批量检测会话列表 -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list"></i> 批量检测会话 ({{ stats_label }})
                </h5>
            </div>
            <div class="card-body">
                {% if page_obj %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>会话名称</th>
                                {% if show_all %}
                                <th>用户</th>
                                {% endif %}
                                <th>图片数量</th>
                                <th>成功/失败</th>
                                <th>成功率</th>
                                <th>状态</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for session in page_obj %}
                            <tr>
                                <td>{{ session.id }}</td>
                                <td>
                                    <strong>{{ session.session_name }}</strong>
                                    <br>
                                    <small class="text-muted">{{ session.model_name }}</small>
                                </td>
                                {% if show_all %}
                                <td>{{ session.user.username }}</td>
                                {% endif %}
                                <td>
                                    <span class="badge bg-primary">{{ session.total_images }}</span>
                                </td>
                                <td>
                                    <span class="badge bg-success">{{ session.completed_images }}</span>
                                    /
                                    <span class="badge bg-danger">{{ session.failed_images }}</span>
                                </td>
                                <td>
                                    <div class="progress" style="width: 60px; height: 20px;">
                                        <div class="progress-bar bg-success" 
                                             style="width: {{ session.success_rate }}%"></div>
                                    </div>
                                    <small>{{ session.success_rate }}%</small>
                                </td>
                                <td>
                                    <span class="badge 
                                        {% if session.status == 'completed' %}bg-success
                                        {% elif session.status == 'partial_completed' %}bg-warning
                                        {% elif session.status == 'failed' %}bg-danger
                                        {% elif session.status == 'processing' %}bg-info
                                        {% else %}bg-secondary{% endif %}">
                                        {{ session.get_status_display }}
                                    </span>
                                </td>
                                <td>
                                    {{ session.created_time|date:"m-d H:i" }}
                                    <br>
                                    <small class="text-muted">{{ session.created_time|date:"Y" }}</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{% url 'batch_result' session.id %}" 
                                           class="btn btn-outline-primary" title="查看结果">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if session.status == 'completed' or session.status == 'partial_completed' %}
                                        <button type="button" class="btn btn-outline-success" 
                                                title="下载报告" onclick="downloadBatchReport({{ session.id }})">
                                            <i class="fas fa-download"></i>
                                        </button>
                                        {% endif %}
                                        {% if session.user == request.user or request.user.is_superuser %}
                                        <button type="button" class="btn btn-outline-danger" 
                                                title="删除" onclick="deleteBatchSession({{ session.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                {% if page_obj.has_other_pages %}
                <nav aria-label="批量检测历史分页">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}&search={{ search_query }}&status={{ status_filter }}{% if show_all %}&show_all=true{% endif %}">上一页</a>
                            </li>
                        {% endif %}
                        
                        {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}&search={{ search_query }}&status={{ status_filter }}{% if show_all %}&show_all=true{% endif %}">{{ num }}</a>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}&search={{ search_query }}&status={{ status_filter }}{% if show_all %}&show_all=true{% endif %}">下一页</a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
                {% else %}
                <div class="text-center py-4">
                    <i class="fas fa-history fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无批量检测历史</p>
                    <a href="{% url 'detect' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> 开始第一次批量检测
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function downloadBatchReport(sessionId) {
    // 实现批量报告下载功能
    alert('批量报告下载功能开发中...');
}

function deleteBatchSession(sessionId) {
    if (confirm('确定要删除这个批量检测会话吗？此操作不可恢复。')) {
        // 实现删除功能
        $.ajax({
            url: `/api/batch-session/${sessionId}/delete/`,
            type: 'DELETE',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(response) {
                if (response.success) {
                    location.reload();
                } else {
                    alert('删除失败：' + (response.error || '未知错误'));
                }
            },
            error: function() {
                alert('删除失败，请重试');
            }
        });
    }
}
</script>
{% endblock %}
