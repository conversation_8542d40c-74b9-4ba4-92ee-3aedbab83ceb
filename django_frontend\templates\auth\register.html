<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        
        .register-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            max-width: 500px;
            width: 90%;
            backdrop-filter: blur(10px);
        }
        
        .logo {
            text-align: center;
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .register-title {
            text-align: center;
            color: #333;
            font-weight: 700;
            margin-bottom: 2rem;
            font-size: 1.8rem;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .form-control {
            border-radius: 15px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-register {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 15px;
            padding: 12px;
            font-weight: 600;
            font-size: 1.1rem;
            width: 100%;
            margin-top: 1rem;
            transition: all 0.3s ease;
        }
        
        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .help-text {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 0.25rem;
        }
        
        .links {
            text-align: center;
            margin-top: 1.5rem;
        }
        
        .links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .links a:hover {
            color: #764ba2;
        }
        
        .alert {
            border-radius: 15px;
            margin-bottom: 1rem;
        }
        
        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            font-size: 1.2rem;
            text-decoration: none;
            transition: all 0.3s ease;
        }
        
        .back-link:hover {
            color: #f8f9fa;
            transform: translateX(-5px);
        }
        
        .password-strength {
            margin-top: 0.5rem;
        }
        
        .strength-bar {
            height: 4px;
            border-radius: 2px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            width: 0%;
        }
        
        .strength-weak { background: #dc3545; }
        .strength-medium { background: #ffc107; }
        .strength-strong { background: #28a745; }
        
        @media (max-width: 768px) {
            .register-container {
                padding: 2rem 1.5rem;
            }
            
            .back-link {
                position: relative;
                top: auto;
                left: auto;
                display: block;
                text-align: center;
                margin-bottom: 1rem;
                color: #667eea;
            }
        }
    </style>
</head>
<body>
    <!-- 返回按钮 -->
    <a href="{% url 'welcome' %}" class="back-link">
        <i class="fas fa-arrow-left me-2"></i>返回首页
    </a>
    
    <div class="register-container">
        <!-- Logo -->
        <div class="logo">
            <i class="fas fa-user-plus"></i>
        </div>
        
        <!-- 标题 -->
        <h2 class="register-title">用户注册</h2>
        
        <!-- 消息提示 -->
        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}
        
        <!-- 注册表单 -->
        <form method="post" novalidate>
            {% csrf_token %}
            
            <!-- 用户名 -->
            <div class="form-floating">
                {{ form.username }}
                <label for="{{ form.username.id_for_label }}">
                    <i class="fas fa-user me-2"></i>{{ form.username.label }}
                </label>
                {% if form.username.help_text %}
                    <div class="help-text">{{ form.username.help_text }}</div>
                {% endif %}
                {% if form.username.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.username.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 邮箱 -->
            <div class="form-floating">
                {{ form.email }}
                <label for="{{ form.email.id_for_label }}">
                    <i class="fas fa-envelope me-2"></i>{{ form.email.label }}
                </label>
                {% if form.email.help_text %}
                    <div class="help-text">{{ form.email.help_text }}</div>
                {% endif %}
                {% if form.email.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.email.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 密码 -->
            <div class="form-floating">
                {{ form.password1 }}
                <label for="{{ form.password1.id_for_label }}">
                    <i class="fas fa-lock me-2"></i>{{ form.password1.label }}
                </label>
                {% if form.password1.help_text %}
                    <div class="help-text">{{ form.password1.help_text }}</div>
                {% endif %}
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <small class="text-muted" id="strengthText">密码强度：未输入</small>
                </div>
                {% if form.password1.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password1.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 确认密码 -->
            <div class="form-floating">
                {{ form.password2 }}
                <label for="{{ form.password2.id_for_label }}">
                    <i class="fas fa-lock me-2"></i>{{ form.password2.label }}
                </label>
                {% if form.password2.help_text %}
                    <div class="help-text">{{ form.password2.help_text }}</div>
                {% endif %}
                {% if form.password2.errors %}
                    <div class="text-danger small mt-1">
                        {% for error in form.password2.errors %}
                            <div>{{ error }}</div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            
            <!-- 注册按钮 -->
            <button type="submit" class="btn btn-primary btn-register">
                <i class="fas fa-user-plus me-2"></i>注册账户
            </button>
        </form>
        
        <!-- 链接 -->
        <div class="links">
            <div class="mb-2">
                已有账户？<a href="{% url 'login' %}">立即登录</a>
            </div>
            <div>
                <a href="{% url 'welcome' %}">返回首页</a>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 密码强度检测
        function checkPasswordStrength(password) {
            let strength = 0;
            let text = '弱';
            let className = 'strength-weak';
            
            if (password.length >= 6) strength += 1;
            if (password.match(/[a-z]/)) strength += 1;
            if (password.match(/[A-Z]/)) strength += 1;
            if (password.match(/[0-9]/)) strength += 1;
            if (password.match(/[^a-zA-Z0-9]/)) strength += 1;
            
            if (strength >= 4) {
                text = '强';
                className = 'strength-strong';
            } else if (strength >= 2) {
                text = '中';
                className = 'strength-medium';
            }
            
            return { strength: strength * 20, text, className };
        }
        
        // 密码输入监听
        document.getElementById('{{ form.password1.id_for_label }}').addEventListener('input', function(e) {
            const password = e.target.value;
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            if (password.length === 0) {
                strengthFill.style.width = '0%';
                strengthFill.className = 'strength-fill';
                strengthText.textContent = '密码强度：未输入';
                return;
            }
            
            const result = checkPasswordStrength(password);
            strengthFill.style.width = result.strength + '%';
            strengthFill.className = 'strength-fill ' + result.className;
            strengthText.textContent = '密码强度：' + result.text;
        });
        
        // 表单验证
        document.querySelector('form').addEventListener('submit', function(e) {
            const username = document.getElementById('{{ form.username.id_for_label }}').value.trim();
            const password1 = document.getElementById('{{ form.password1.id_for_label }}').value;
            const password2 = document.getElementById('{{ form.password2.id_for_label }}').value;
            
            if (!username) {
                e.preventDefault();
                alert('请输入用户名');
                return;
            }
            
            if (!password1) {
                e.preventDefault();
                alert('请输入密码');
                return;
            }
            
            if (password1 !== password2) {
                e.preventDefault();
                alert('两次输入的密码不一致');
                return;
            }
        });
    </script>
</body>
</html>
