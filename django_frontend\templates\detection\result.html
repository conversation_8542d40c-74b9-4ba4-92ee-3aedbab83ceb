{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-chart-line"></i> 检测结果 #{{ record.id }}
            </h2>
            <div>
                <a href="{% url 'index' %}" class="btn btn-outline-primary">
                    <i class="fas fa-plus"></i> 新检测
                </a>
                <a href="{% url 'history' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-list"></i> 历史记录
                </a>
            </div>
        </div>
        
        <!-- 状态信息 -->
        {% if record.status == 'completed' %}
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> 检测完成！
                处理时间: {{ record.processing_time|floatformat:2 }} 秒
            </div>
        {% elif record.status == 'failed' %}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> 检测失败: {{ record.error_message }}
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-spinner fa-spin"></i> 正在处理中...
            </div>
        {% endif %}
    </div>
</div>

{% if record.status == 'completed' %}
<div class="row">
    <!-- 图片对比 -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-images"></i> 检测结果对比
                </h5>
            </div>
            <div class="card-body">
                <div class="result-comparison">
                    <!-- 原始图片 -->
                    <div class="flex-fill">
                        <h6 class="text-center mb-3">原始图片</h6>
                        <div class="text-center">
                            <img src="{{ record.original_image.url }}" 
                                 class="img-fluid rounded shadow" 
                                 alt="原始图片"
                                 style="max-height: 400px;">
                        </div>
                    </div>
                    
                    <!-- 检测结果 -->
                    <div class="flex-fill">
                        <h6 class="text-center mb-3">检测结果</h6>
                        <div class="text-center">
                            {% if record.result_image %}
                                <img src="{{ record.result_image.url }}" 
                                     class="img-fluid rounded shadow" 
                                     alt="检测结果"
                                     style="max-height: 400px;">
                            {% else %}
                                <div class="bg-light rounded d-flex align-items-center justify-content-center" 
                                     style="height: 300px;">
                                    <div class="text-center text-muted">
                                        <i class="fas fa-image fa-3x mb-3"></i>
                                        <p>暂无结果图片</p>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 大模型API分析模块 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-robot"></i> AI智能分析
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <!-- 左侧：输入区域 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="llmPrompt" class="form-label">
                                <i class="fas fa-comment-dots"></i> 分析提示词
                            </label>
                            <textarea class="form-control llm-prompt-textarea"
                                      id="llmPrompt"
                                      rows="3"
                                      placeholder="请输入您想要AI分析的问题，例如：
• 分析这张图片中的口罩佩戴情况
• 给出口罩佩戴的改进建议
• 评估整体的防疫合规性">基于检测结果，请分析这张图片中的口罩佩戴情况，并给出专业的评估和建议。</textarea>
                        </div>

                        <div class="mb-3">
                            <label for="llmModel" class="form-label">
                                <i class="fas fa-brain"></i> 选择AI模型
                            </label>
                            <select class="form-select llm-model-select" id="llmModel">
                                <option value="Qwen/QwQ-32B" selected>Qwen QwQ-32B (强大推理)</option>
                                <option value="Qwen/Qwen2.5-7B-Instruct">Qwen 2.5-7B-Instruct (快速响应)</option>
                                <option value="Qwen/Qwen2.5-14B-Instruct">Qwen 2.5-14B-Instruct (平衡性能)</option>
                                <option value="deepseek-ai/DeepSeek-V2.5">DeepSeek V2.5 (深度思考)</option>
                            </select>
                        </div>

                        <div class="d-grid">
                            <button type="button"
                                    class="btn btn-primary llm-analysis-btn"
                                    id="startLLMAnalysis"
                                    onclick="startLLMAnalysis()">
                                <i class="fas fa-magic"></i> 开始AI分析
                            </button>
                        </div>
                    </div>

                    <!-- 右侧：结果显示区域 -->
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label">
                                <i class="fas fa-lightbulb"></i> AI分析结果
                            </label>
                        </div>

                        <!-- 加载状态 -->
                        <div id="llmLoadingState" class="text-center py-4 llm-loading-animation" style="display: none;">
                            <div class="spinner-border text-primary mb-3" role="status">
                                <span class="visually-hidden">分析中...</span>
                            </div>
                            <p class="text-muted">AI正在分析中，请稍候...</p>
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated"
                                     role="progressbar"
                                     style="width: 100%"></div>
                            </div>
                        </div>

                        <!-- 结果显示 -->
                        <div id="llmResultArea" class="llm-result-area" style="min-height: 180px; max-height: 300px; overflow-y: auto; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; background-color: #f8f9fa;">
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-robot fa-2x mb-3 ai-icon-animated"></i>
                                <p>点击"开始AI分析"按钮，获取专业的智能分析结果</p>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="mt-3 llm-action-buttons d-flex gap-2">
                            <button type="button"
                                    class="btn btn-outline-secondary btn-sm"
                                    id="copyLLMResult"
                                    onclick="copyLLMResult()"
                                    style="display: none;">
                                <i class="fas fa-copy"></i> 复制结果
                            </button>
                            <button type="button"
                                    class="btn btn-outline-info btn-sm"
                                    id="downloadLLMResult"
                                    onclick="downloadLLMResult()"
                                    style="display: none;">
                                <i class="fas fa-download"></i> 下载PDF报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="col-lg-4">
        <!-- 检测摘要 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-pie"></i> 检测摘要
                </h6>
            </div>
            <div class="card-body">
                <div class="row text-center mb-3">
                    <div class="col-12">
                        <h3 class="text-primary">{{ record.total_detections }}</h3>
                        <small class="text-muted">总检测数量</small>
                    </div>
                </div>
                
                <div class="row text-center">
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-success">{{ record.with_mask_count }}</h5>
                            <small class="text-muted">正确</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <div class="border-end">
                            <h5 class="text-danger">{{ record.without_mask_count }}</h5>
                            <small class="text-muted">未戴</small>
                        </div>
                    </div>
                    <div class="col-4">
                        <h5 class="text-warning">{{ record.incorrect_mask_count }}</h5>
                        <small class="text-muted">错误</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="mb-2">
                    <div class="d-flex justify-content-between">
                        <span>合规率:</span>
                        <strong class="text-success">{{ detection_summary.compliance_rate }}%</strong>
                    </div>
                </div>
                
                <div class="progress">
                    <div class="progress-bar bg-success" 
                         style="width: {{ detection_summary.compliance_rate }}%"></div>
                </div>
            </div>
        </div>
        
        <!-- 检测参数 -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-cog"></i> 检测参数
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <strong>模型:</strong> {{ record.model_name }}
                </div>
                <div class="mb-2">
                    <strong>置信度:</strong> {{ record.confidence_threshold }}
                </div>
                <div class="mb-2">
                    <strong>IOU阈值:</strong> {{ record.iou_threshold }}
                </div>
                <div class="mb-2">
                    <strong>图像尺寸:</strong> {{ record.image_size }}x{{ record.image_size }}
                </div>
                <div class="mb-2">
                    <strong>检测时间:</strong> {{ record.upload_time|date:"Y-m-d H:i:s" }}
                </div>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="card">
            <div class="card-body">
                <div class="d-grid gap-2">
                    {% if record.result_image %}
                    <a href="{{ record.result_image.url }}" 
                       class="btn btn-success" 
                       download="detection_result_{{ record.id }}.png">
                        <i class="fas fa-download"></i> 下载结果图片
                    </a>
                    {% endif %}
                    
                    <button class="btn btn-info" onclick="showDetails()">
                        <i class="fas fa-info-circle"></i> 查看详细数据
                    </button>
                    
                    <form method="post" action="{% url 'delete_record' record.id %}" 
                          onsubmit="return confirm('确定要删除这条记录吗？');">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-outline-danger w-100">
                            <i class="fas fa-trash"></i> 删除记录
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- 详细检测数据模态框 -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-list-alt"></i> 详细检测数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                {% if record.detection_details %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>序号</th>
                                <th>类别</th>
                                <th>置信度</th>
                                <th>位置 (x, y)</th>
                                <th>尺寸 (w, h)</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detection in record.detection_details %}
                            <tr>
                                <td>{{ forloop.counter }}</td>
                                <td>
                                    {% if detection.class_name == 'with_mask' %}
                                        <span class="badge bg-success">正确戴口罩</span>
                                    {% elif detection.class_name == 'without_mask' %}
                                        <span class="badge bg-danger">未戴口罩</span>
                                    {% elif detection.class_name == 'mask_weared_incorrect' %}
                                        <span class="badge bg-warning">错误戴口罩</span>
                                    {% else %}
                                        <span class="badge bg-secondary">{{ detection.class_name }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ detection.confidence|floatformat:3 }}</td>
                                <td>{{ detection.x_center|floatformat:3 }}, {{ detection.y_center|floatformat:3 }}</td>
                                <td>{{ detection.width|floatformat:3 }}, {{ detection.height|floatformat:3 }}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted text-center">暂无详细检测数据</p>
                {% endif %}
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function showDetails() {
    $('#detailsModal').modal('show');
}

// 如果状态不是完成，定期检查状态
{% if record.status != 'completed' and record.status != 'failed' %}
function checkStatus() {
    $.get('{% url "detection_status" record.id %}', function(data) {
        if (data.status === 'completed' || data.status === 'failed') {
            location.reload();
        }
    });
}

// 每3秒检查一次状态
setInterval(checkStatus, 3000);
{% endif %}

// 大模型API相关功能
let llmAnalysisInProgress = false;
let llmResultContent = '';
let currentModelUsed = '';

// 开始LLM分析
function startLLMAnalysis() {
    console.log('startLLMAnalysis 被调用');

    if (llmAnalysisInProgress) {
        console.log('分析正在进行中，跳过');
        return;
    }

    const prompt = $('#llmPrompt').val().trim();
    const model = $('#llmModel').val();

    console.log('提示词:', prompt);
    console.log('模型:', model);

    if (!prompt) {
        alert('请输入分析提示词');
        return;
    }

    // 设置加载状态
    llmAnalysisInProgress = true;
    $('#startLLMAnalysis').prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 分析中...');

    // 显示加载状态
    $('#llmResultArea').html(`
        <div class="text-center py-4">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <div class="mt-2">AI正在分析中，请稍候...</div>
        </div>
    `);

    // 准备请求数据
    const requestData = {
        prompt: prompt,
        model: model,
        record_id: {{ record.id }},
        detection_data: {
            total_detections: {{ record.total_detections }},
            with_mask_count: {{ record.with_mask_count }},
            without_mask_count: {{ record.without_mask_count }},
            incorrect_mask_count: {{ record.incorrect_mask_count }},
            compliance_rate: {{ detection_summary.compliance_rate }},
            model_name: '{{ record.model_name }}',
            confidence_threshold: {{ record.confidence_threshold }}
        }
    };

    console.log('发送请求数据:', requestData);

    // 直接使用AJAX调用
    $.ajax({
        url: '/api/llm-analysis/',
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        timeout: 180000, // 增加到3分钟，给AI更多时间生成完整回答
        success: function(response) {
            console.log('成功响应:', response);
            llmAnalysisInProgress = false;
            $('#startLLMAnalysis').prop('disabled', false).html('<i class="fas fa-magic"></i> 开始AI分析');

            if (response.success) {
                console.log('API响应中的model_used:', response.model_used);
                console.log('API响应中的api_provider:', response.api_provider);

                // 使用displayLLMResult函数来正确处理结果
                displayLLMResult(response.analysis, response.model_used, response.api_provider);
                llmResultContent = response.analysis;
                currentModelUsed = response.model_used;
                console.log('保存的模型信息:', currentModelUsed);
            } else {
                displayLLMError(response.error || '分析失败，请重试');
            }
        },
        error: function(xhr, status, error) {
            console.error('请求失败:', xhr, status, error);
            llmAnalysisInProgress = false;
            $('#startLLMAnalysis').prop('disabled', false).html('<i class="fas fa-magic"></i> 开始AI分析');

            let errorMsg = '网络错误，请检查连接后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            } else if (status === 'timeout') {
                errorMsg = '请求超时，请重试';
            }

            $('#llmResultArea').html(`
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle"></i> 请求失败</h6>
                    <p>${errorMsg}</p>
                    <small>状态: ${status}, 错误: ${error}</small>
                </div>
            `);
        }
    });
}

// 传统分析方式（备用）
function startTraditionalAnalysis(requestData) {
    console.log('开始传统分析，请求数据:', requestData);

    // 发送API请求
    $.ajax({
        url: '/api/llm-analysis/',
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        timeout: 180000, // 增加到3分钟，给AI更多时间生成完整回答
        success: function(response) {
            console.log('LLM API响应:', response);
            if (response.success) {
                displayLLMResult(response.analysis, response.model_used, response.api_provider);
                llmResultContent = response.analysis;
                currentModelUsed = response.model_used;
            } else {
                displayLLMError(response.error || '分析失败，请重试');
            }
        },
        error: function(xhr, status, error) {
            console.error('LLM API错误:', xhr, status, error);
            let errorMsg = '网络错误，请检查连接后重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMsg = xhr.responseJSON.error;
            } else if (status === 'timeout') {
                errorMsg = '请求超时，请重试';
            }
            displayLLMError(errorMsg);
        },
        complete: function() {
            setLLMLoadingState(false);
        }
    });
}

// 流式分析函数
function startStreamingAnalysis(requestData) {
    const $resultArea = $('#llmResultArea');

    // 初始化流式显示
    $resultArea.html(`
        <div class="llm-loading">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">分析中...</span>
            </div>
            <div class="mt-2">AI正在分析中，请稍候...</div>
        </div>
    `);

    let streamContent = '';
    let modelUsed = '';

    // 发送请求并处理流式响应
    fetch('/api/llm-analysis-stream/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        body: JSON.stringify(requestData)
    }).then(response => {
        if (!response.ok) {
            throw new Error('网络请求失败');
        }

        // 处理流式响应
        const reader = response.body.getReader();
        const decoder = new TextDecoder();

        function readStream() {
            reader.read().then(({ done, value }) => {
                if (done) {
                    // 流式输出完成
                    finishStreamingAnalysis(streamContent, modelUsed);
                    return;
                }

                const chunk = decoder.decode(value, { stream: true });
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const dataStr = line.substring(6);
                        if (dataStr.trim() === '') continue;

                        try {
                            const data = JSON.parse(dataStr);
                            handleStreamEvent(data);
                        } catch (e) {
                            console.error('解析流式数据失败:', e);
                        }
                    }
                }

                readStream();
            }).catch(error => {
                console.error('读取流式数据失败:', error);
                displayLLMError('流式分析失败，请重试');
                setLLMLoadingState(false);
            });
        }

        readStream();
    }).catch(error => {
        console.error('启动流式分析失败:', error);
        displayLLMError('启动分析失败，请重试');
        setLLMLoadingState(false);
    });

    // 处理流式事件
    function handleStreamEvent(data) {
        switch (data.type) {
            case 'start':
                modelUsed = data.model;
                streamContent = '';
                // 显示开始分析状态
                $resultArea.html(`
                    <div class="llm-loading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">分析中...</span>
                        </div>
                        <div class="mt-2">使用 ${data.model} 模型分析中...</div>
                    </div>
                `);
                break;

            case 'content':
                streamContent += data.content;
                // 实时更新显示内容
                updateStreamingContent(streamContent, modelUsed);
                break;

            case 'done':
                // 分析完成
                finishStreamingAnalysis(streamContent, modelUsed);
                break;

            case 'error':
                displayLLMError(data.error);
                setLLMLoadingState(false);
                break;
        }
    }
}

// 更新流式内容显示
function updateStreamingContent(content, modelUsed) {
    const $resultArea = $('#llmResultArea');
    const formattedContent = formatAnalysisResult(content);

    let modelInfo = '';
    if (modelUsed) {
        modelInfo = `
            <div class="mb-2">
                <small class="text-muted">
                    <i class="fas fa-microchip text-primary"></i>
                    SiliconFlow - ${modelUsed}
                </small>
            </div>
        `;
    }

    $resultArea.html(`
        <div class="llm-result-success">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-robot text-primary me-2"></i>
                <strong>AI分析中...</strong>
            </div>
            ${modelInfo}
            <div class="llm-result-content streaming-text">${formattedContent}</div>
        </div>
    `).addClass('has-content');

    // 自动滚动到底部
    $resultArea.scrollTop($resultArea[0].scrollHeight);
}

// 完成流式分析
function finishStreamingAnalysis(content, modelUsed) {
    const $resultArea = $('#llmResultArea');
    const $copyBtn = $('#copyLLMResult');
    const $downloadBtn = $('#downloadLLMResult');

    const formattedContent = formatAnalysisResult(content);

    let modelInfo = '';
    if (modelUsed) {
        modelInfo = `
            <div class="mb-2">
                <small class="text-muted">
                    <i class="fas fa-microchip text-primary"></i>
                    SiliconFlow - ${modelUsed}
                </small>
            </div>
        `;
    }

    $resultArea.html(`
        <div class="llm-result-success">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>AI分析完成</strong>
            </div>
            ${modelInfo}
            <div class="llm-result-content">${formattedContent}</div>
        </div>
    `).addClass('has-content');

    // 保存结果内容
    llmResultContent = content;
    currentModelUsed = modelUsed;

    // 显示操作按钮
    $copyBtn.show();
    $downloadBtn.show();

    // 设置加载状态为完成
    setLLMLoadingState(false);
}

// 设置加载状态
function setLLMLoadingState(loading) {
    llmAnalysisInProgress = loading;
    const $button = $('#startLLMAnalysis');
    const $resultArea = $('#llmResultArea');

    if (loading) {
        $button.prop('disabled', true)
               .html('<i class="fas fa-spinner fa-spin"></i> 分析中...');

        // 显示加载状态
        $resultArea.html(`
            <div class="llm-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">分析中...</span>
                </div>
                <div class="mt-2">AI正在分析中，请稍候...</div>
            </div>
        `).removeClass('has-content');
    } else {
        $button.prop('disabled', false)
               .html('<i class="fas fa-magic"></i> 开始AI分析');
    }
}

// 显示LLM分析结果
function displayLLMResult(analysis, modelUsed, apiProvider) {
    const $resultArea = $('#llmResultArea');
    const $copyBtn = $('#copyLLMResult');
    const $downloadBtn = $('#downloadLLMResult');

    // 格式化分析结果
    const formattedAnalysis = formatAnalysisResult(analysis);

    // 构建模型信息显示
    let modelInfo = '';
    if (modelUsed && apiProvider) {
        const providerIcon = apiProvider === 'SiliconFlow' ? 'fas fa-microchip' : 'fas fa-robot';
        const providerColor = apiProvider === 'SiliconFlow' ? 'text-primary' : 'text-info';
        modelInfo = `
            <div class="mb-2">
                <small class="text-muted">
                    <i class="${providerIcon} ${providerColor}"></i>
                    ${apiProvider} - ${modelUsed}
                </small>
            </div>
        `;
    }

    $resultArea.html(`
        <div class="llm-result-success">
            <div class="d-flex align-items-center mb-2">
                <i class="fas fa-check-circle text-success me-2"></i>
                <strong>AI分析完成</strong>
            </div>
            ${modelInfo}
            <div class="llm-result-content">${formattedAnalysis}</div>
        </div>
    `).addClass('has-content');

    // 保存结果内容和模型信息
    llmResultContent = analysis;
    currentModelUsed = modelUsed;

    // 显示操作按钮
    $copyBtn.show();
    $downloadBtn.show();
}

// 显示LLM错误信息
function displayLLMError(error) {
    const $resultArea = $('#llmResultArea');
    console.log('显示LLM错误:', error);

    $resultArea.html(`
        <div class="llm-result-error">
            <div class="alert alert-warning">
                <h6><i class="fas fa-exclamation-triangle"></i> 分析失败</h6>
                <p class="mb-2">${error}</p>
                <button type="button" class="btn btn-outline-primary btn-sm me-2" onclick="retryLLMAnalysis()">
                    <i class="fas fa-redo"></i> 重试
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearLLMResult()">
                    <i class="fas fa-times"></i> 清除
                </button>
            </div>
        </div>
    `).removeClass('has-content');
}

// 重试LLM分析
function retryLLMAnalysis() {
    if (llmAnalysisInProgress) {
        return;
    }

    // 清除之前的错误信息
    $('#llmResultArea').empty();

    // 重新开始分析
    startLLMAnalysis();
}

// 清除LLM结果
function clearLLMResult() {
    $('#llmResultArea').empty().removeClass('has-content');
    llmResultContent = '';
}

// 格式化分析结果
function formatAnalysisResult(analysis) {
    if (!analysis) return '';

    // 先进行HTML转义
    let formatted = analysis
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;');

    // 处理标题（# ## ###）
    formatted = formatted
        .replace(/^### (.*$)/gm, '<h6 class="text-primary fw-bold mt-3 mb-2">$1</h6>')
        .replace(/^## (.*$)/gm, '<h5 class="text-primary fw-bold mt-3 mb-2">$1</h5>')
        .replace(/^# (.*$)/gm, '<h4 class="text-primary fw-bold mt-3 mb-2">$1</h4>');

    // 处理粗体和斜体
    formatted = formatted
        .replace(/\*\*(.*?)\*\*/g, '<strong class="text-dark">$1</strong>')
        .replace(/\*(.*?)\*/g, '<em class="text-muted">$1</em>');

    // 处理列表项
    formatted = formatted
        .replace(/^- (.*$)/gm, '<div class="mb-1"><i class="fas fa-circle text-primary me-2" style="font-size: 0.5em; vertical-align: middle;"></i>$1</div>')
        .replace(/^\* (.*$)/gm, '<div class="mb-1"><i class="fas fa-circle text-primary me-2" style="font-size: 0.5em; vertical-align: middle;"></i>$1</div>');

    // 处理数字列表
    formatted = formatted.replace(/^(\d+)\. (.*$)/gm, '<div class="mb-1"><span class="badge bg-primary me-2" style="font-size: 0.7em;">$1</span>$2</div>');

    // 处理代码块（如果有）
    formatted = formatted
        .replace(/`([^`]+)`/g, '<code class="bg-light text-dark px-1 rounded">$1</code>');

    // 处理换行
    formatted = formatted.replace(/\n\n/g, '<br><br>').replace(/\n/g, '<br>');

    // 处理特殊关键词高亮
    formatted = formatted
        .replace(/(建议|推荐|注意|重要|警告|提醒)/g, '<span class="badge bg-warning text-dark me-1">$1</span>')
        .replace(/(优秀|良好|合格|达标)/g, '<span class="badge bg-success me-1">$1</span>')
        .replace(/(不合格|不达标|需要改进|问题)/g, '<span class="badge bg-danger me-1">$1</span>')
        .replace(/(口罩|佩戴|检测|分析)/g, '<span class="text-primary fw-semibold">$1</span>');

    return formatted;
}

// 复制LLM结果
function copyLLMResult() {
    if (!llmResultContent) {
        alert('暂无结果可复制');
        return;
    }

    // 创建临时文本区域
    const textArea = document.createElement('textarea');
    textArea.value = llmResultContent;
    document.body.appendChild(textArea);
    textArea.select();

    try {
        document.execCommand('copy');
        // 显示成功提示
        const $btn = $('#copyLLMResult');
        const originalText = $btn.html();
        $btn.html('<i class="fas fa-check"></i> 已复制').addClass('btn-success').removeClass('btn-outline-secondary');

        setTimeout(() => {
            $btn.html(originalText).removeClass('btn-success').addClass('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        alert('复制失败，请手动选择文本复制');
    }

    document.body.removeChild(textArea);
}

// 下载LLM分析报告 (PDF格式)
function downloadLLMResult() {
    if (!llmResultContent) {
        alert('暂无结果可下载');
        return;
    }

    // 显示下载状态
    const $downloadBtn = $('#downloadLLMResult');
    const originalText = $downloadBtn.html();
    $downloadBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 生成PDF中...');

    // 准备请求数据
    const requestData = {
        record_id: {{ record.id }},
        llm_content: llmResultContent,
        model_used: currentModelUsed || null
    };

    console.log('PDF下载请求数据:', requestData);
    console.log('当前使用的模型:', currentModelUsed);

    // 发送PDF生成请求
    $.ajax({
        url: '/api/llm-pdf-download/',
        type: 'POST',
        data: JSON.stringify(requestData),
        contentType: 'application/json',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        xhrFields: {
            responseType: 'blob'  // 重要：设置响应类型为blob
        },
        timeout: 60000, // 60秒超时
        success: function(data, textStatus, xhr) {
            try {
                // 从响应头获取文件名
                const contentDisposition = xhr.getResponseHeader('Content-Disposition');
                let filename = 'mask_detection_ai_report.pdf';
                if (contentDisposition) {
                    const filenameMatch = contentDisposition.match(/filename="(.+)"/);
                    if (filenameMatch) {
                        filename = filenameMatch[1];
                    }
                }

                // 创建下载链接
                const blob = new Blob([data], { type: 'application/pdf' });
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = filename;

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                window.URL.revokeObjectURL(url);

                console.log('PDF报告下载成功');

                // 恢复按钮状态
                $downloadBtn.prop('disabled', false).html(originalText);

            } catch (error) {
                console.error('PDF下载处理失败:', error);
                alert('PDF下载处理失败，请重试');
                $downloadBtn.prop('disabled', false).html(originalText);
            }
        },
        error: function(xhr, status, error) {
            console.error('PDF生成失败:', error);

            let errorMessage = 'PDF生成失败，请重试';
            if (xhr.responseJSON && xhr.responseJSON.error) {
                errorMessage = xhr.responseJSON.error;
            } else if (status === 'timeout') {
                errorMessage = 'PDF生成超时，请重试';
            }

            alert(errorMessage);

            // 恢复按钮状态
            $downloadBtn.prop('disabled', false).html(originalText);
        }
    });
}

// 页面加载完成后的初始化
$(document).ready(function() {
    // 为提示词输入框添加字符计数
    $('#llmPrompt').on('input', function() {
        const length = $(this).val().length;
        const maxLength = 1000;

        if (length > maxLength * 0.8) {
            $(this).addClass('border-warning');
        } else {
            $(this).removeClass('border-warning');
        }
    });

    // 模型选择变化时的提示
    $('#llmModel').on('change', function() {
        const model = $(this).val();
        let tip = '';

        switch(model) {
            case 'Qwen/QwQ-32B':
                tip = '强大的推理模型，适合复杂分析';
                break;
            case 'Qwen/Qwen2.5-7B-Instruct':
                tip = '快速响应，适合一般分析';
                break;
            case 'Qwen/Qwen2.5-14B-Instruct':
                tip = '平衡性能，综合分析能力强';
                break;
            case 'deepseek-ai/DeepSeek-V2.5':
                tip = '深度思考模型，适合专业分析';
                break;
        }

        // 可以在这里显示模型提示信息
        console.log('选择模型:', model, '-', tip);
    });
});
</script>
{% endblock %}
