# 模型评估脚本使用说明

本目录包含完整的YOLO口罩检测模型评估工具，用于满足学术报告4.2节"模型测试"的要求。

## 📁 文件说明

### 主要脚本
- `model_evaluation.py` - 完整的模型评估脚本（Python版本）
- `model_evaluation_analysis.ipynb` - Jupyter Notebook版本（包含可视化）
- `test_model_comparison.py` - 测试模型对比功能的脚本

### 支持文件
- `README_evaluation.md` - 本说明文件

## 🎯 功能特性

### 1. 交叉验证 (Cross Validation)
- **目的**: 评估模型稳定性和泛化能力
- **方法**: K折交叉验证（默认3折）
- **输出**: 
  - 各折验证的性能指标
  - 均值和标准差统计
  - 可视化箱线图

### 2. 性能对比 (Performance Comparison)
- **目的**: 与基线模型或其他版本比较
- **功能**: 
  - 多模型性能对比
  - 效率指标计算（mAP/MB, mAP/ms）
  - 自动识别最佳模型
  - 错误处理和数据验证

### 3. 错误分析 (Error Analysis)
- **目的**: 识别模型不足，指导改进方向
- **分析类型**:
  - 假阳性 (False Positives)
  - 假阴性 (False Negatives)
  - 低置信度预测
  - 高置信度错误

### 4. 混淆矩阵分析
- **目的**: 详细的分类性能分析
- **输出**: 
  - 混淆矩阵热力图
  - 分类报告
  - 各类别性能指标

## 🚀 使用方法

### 方法1: 使用Python脚本（推荐）

```bash
# 进入yoloserver目录
cd yoloserver

# 完整评估（包含所有功能）
python scripts/model_evaluation.py

# 自定义参数
python scripts/model_evaluation.py --cv_folds 5 --error_samples 50

# 跳过某些步骤
python scripts/model_evaluation.py --skip_cv --skip_error
```

### 方法2: 使用Jupyter Notebook

```bash
# 启动Jupyter
jupyter notebook scripts/model_evaluation_analysis.ipynb
```

### 方法3: 测试特定功能

```bash
# 测试模型对比功能
python scripts/test_model_comparison.py
```

## 📊 输出结果

### 生成的文件
所有结果保存在 `yoloserver/evaluation_results/` 目录下：

- `cross_validation_results.csv` - 交叉验证数据
- `cross_validation_results.png` - 交叉验证可视化
- `model_comparison.csv` - 模型对比数据
- `model_comparison.png` - 模型对比可视化
- `confusion_matrix.png` - 混淆矩阵图
- `comprehensive_evaluation_report.json` - 综合评估报告

### 控制台输出
- 实时进度显示
- 关键指标摘要
- 最佳模型识别
- 改进建议

## ⚙️ 参数说明

### 命令行参数
- `--cv_folds`: 交叉验证折数（默认3）
- `--error_samples`: 错误分析样本数（默认30）
- `--skip_cv`: 跳过交叉验证
- `--skip_comparison`: 跳过模型对比
- `--skip_error`: 跳过错误分析

### 配置要求
- 确保 `configs/data.yaml` 配置正确
- 确保 `models/checkpoints/` 目录有训练好的模型
- 确保数据集路径正确

## 🔧 故障排除

### 常见问题

1. **找不到模型文件**
   ```
   错误: 未找到可用的模型文件
   ```
   - 检查 `models/checkpoints/` 目录
   - 确保有 `.pt` 格式的模型文件

2. **测试集不存在**
   ```
   ⚠️ 测试集不存在或为空，使用验证集进行模型对比
   ```
   - 这是正常行为，脚本会自动使用验证集

3. **内存不足**
   - 减少 `--error_samples` 参数
   - 减少 `--cv_folds` 参数

4. **CUDA错误**
   - 检查GPU内存
   - 考虑使用CPU模式

### 调试模式
```bash
# 启用详细输出
python scripts/model_evaluation.py --cv_folds 1 --error_samples 10
```

## 📈 报告使用建议

### 学术报告4.2节内容
1. **交叉验证结果**
   - 展示模型稳定性
   - 使用均值±标准差格式
   - 包含可视化图表

2. **性能对比结果**
   - 与基线模型比较
   - 突出模型优势
   - 包含效率分析

3. **错误分析结果**
   - 讨论模型局限性
   - 提出改进方向
   - 展示典型错误案例

### 图表使用
- 所有生成的PNG图片可直接用于报告
- JSON报告包含详细数据用于表格制作
- CSV文件可用于进一步分析

## 🔄 更新日志

### v1.1 (当前版本)
- ✅ 修复性能对比模块的错误处理
- ✅ 添加自动数据集检测（test/val）
- ✅ 改进可视化效果
- ✅ 添加效率指标计算
- ✅ 增强错误提示和调试信息

### v1.0
- ✅ 基础交叉验证功能
- ✅ 模型性能对比
- ✅ 错误分析
- ✅ 混淆矩阵生成
