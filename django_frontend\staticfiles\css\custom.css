/* 自定义样式 */

/* 上传区域样式 */
.upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background-color: #fafafa;
}

.upload-area:hover {
    border-color: #0d6efd;
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    box-shadow: 0 0 20px rgba(13, 110, 253, 0.2);
}

/* 隐藏默认文件输入 */
.upload-area input[type="file"] {
    display: none;
}

/* 预览图片样式 */
.preview-image {
    max-width: 100%;
    max-height: 300px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.preview-image:hover {
    transform: scale(1.02);
}

/* 检测卡片样式 */
.detection-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.detection-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #0d6efd;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.8em;
    font-weight: 500;
}

/* 进度条容器 */
.progress-container {
    display: none;
    margin-top: 20px;
}

.progress {
    height: 8px;
    border-radius: 4px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 结果对比样式 */
.result-comparison {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.result-comparison > div {
    flex: 1;
}

@media (max-width: 768px) {
    .result-comparison {
        flex-direction: column;
        gap: 15px;
    }
}

/* 统计卡片样式 */
.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.stats-card .card-body {
    padding: 1.5rem;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: #fff !important;
}

/* 按钮样式增强 */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    border: none;
}

.btn-danger {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    border: none;
}

.btn-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border: none;
}

/* 卡片样式增强 */
.card {
    border: 1px solid #e9ecef;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0 !important;
}

/* 表格样式 */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* 模态框样式 */
.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 40px rgba(0,0,0,0.2);
}

.modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    border-radius: 10px 10px 0 0;
}

/* 分页样式 */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #6c757d;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* 徽章样式 */
.badge {
    font-weight: 500;
    padding: 0.5em 0.8em;
    border-radius: 6px;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 576px) {
    .container {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
    
    .upload-area {
        padding: 20px;
    }
}

/* LLM分析结果区域样式 */
.llm-result-area {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    position: relative;
}

.llm-result-area:hover {
    border-color: #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.llm-result-area.has-content {
    background-color: #ffffff;
    border-color: #d1ecf1;
}

/* 滚动条样式 */
.llm-result-area::-webkit-scrollbar {
    width: 8px;
}

.llm-result-area::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.llm-result-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
    transition: background 0.3s ease;
}

.llm-result-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* LLM结果内容样式 */
.llm-result-content {
    line-height: 1.6;
    color: #333;
    font-size: 14px;
    white-space: pre-wrap;
    word-wrap: break-word;
}

.llm-result-success {
    animation: fadeInUp 0.5s ease-out;
}

.llm-result-error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 6px;
    padding: 12px;
    margin: 10px 0;
}

/* 加载状态样式 */
.llm-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 30px;
    color: #6c757d;
}

.llm-loading .spinner-border {
    margin-bottom: 15px;
}

/* 流式输出动画 */
.streaming-text {
    position: relative;
}

.streaming-text::after {
    content: '|';
    animation: blink 1s infinite;
    color: #007bff;
    font-weight: bold;
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* AI图标动画 */
.ai-icon-animated {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .upload-area {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .upload-area:hover {
        background-color: #374151;
        border-color: #667eea;
    }

    .llm-result-area {
        background-color: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }

    .llm-result-area.has-content {
        background-color: #374151;
        border-color: #4a90a4;
    }

    .llm-result-content {
        color: #e2e8f0;
    }
}
