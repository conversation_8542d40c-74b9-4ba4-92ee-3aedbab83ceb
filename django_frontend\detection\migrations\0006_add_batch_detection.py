# Generated by Django 5.2.3 on 2025-07-02 16:16

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        (
            "detection",
            "0005_rename_detection_l_usernam_b8e5a5_idx_detection_l_usernam_27b248_idx_and_more",
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name="detectionrecord",
            name="batch_index",
            field=models.IntegerField(
                blank=True,
                help_text="在批量检测会话中的序号",
                null=True,
                verbose_name="批次内序号",
            ),
        ),
        migrations.AddField(
            model_name="detectionrecord",
            name="is_batch_detection",
            field=models.BooleanField(
                default=False,
                help_text="标识是否为批量检测的一部分",
                verbose_name="是否为批量检测",
            ),
        ),
        migrations.CreateModel(
            name="BatchDetectionSession",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "session_name",
                    models.CharField(
                        help_text="批量检测会话的名称",
                        max_length=200,
                        verbose_name="会话名称",
                    ),
                ),
                (
                    "created_time",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_time",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                (
                    "total_images",
                    models.IntegerField(default=0, verbose_name="总图片数量"),
                ),
                (
                    "completed_images",
                    models.IntegerField(default=0, verbose_name="已完成图片数量"),
                ),
                (
                    "failed_images",
                    models.IntegerField(default=0, verbose_name="失败图片数量"),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "等待处理"),
                            ("processing", "处理中"),
                            ("completed", "已完成"),
                            ("partial_completed", "部分完成"),
                            ("failed", "处理失败"),
                            ("cancelled", "已取消"),
                        ],
                        default="pending",
                        max_length=20,
                        verbose_name="处理状态",
                    ),
                ),
                (
                    "model_name",
                    models.CharField(
                        default="yolo11n-seg.pt",
                        max_length=100,
                        verbose_name="使用的模型",
                    ),
                ),
                (
                    "confidence_threshold",
                    models.FloatField(default=0.25, verbose_name="置信度阈值"),
                ),
                (
                    "iou_threshold",
                    models.FloatField(default=0.45, verbose_name="IOU阈值"),
                ),
                (
                    "image_size",
                    models.IntegerField(default=640, verbose_name="图像尺寸"),
                ),
                (
                    "start_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="开始处理时间"
                    ),
                ),
                (
                    "end_time",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="结束处理时间"
                    ),
                ),
                (
                    "total_processing_time",
                    models.FloatField(
                        blank=True, null=True, verbose_name="总处理时间(秒)"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(blank=True, null=True, verbose_name="错误信息"),
                ),
                (
                    "user",
                    models.ForeignKey(
                        help_text="批量检测会话所属用户",
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                        verbose_name="用户",
                    ),
                ),
            ],
            options={
                "verbose_name": "批量检测会话",
                "verbose_name_plural": "批量检测会话",
                "ordering": ["-created_time"],
            },
        ),
        migrations.AddField(
            model_name="detectionrecord",
            name="batch_session",
            field=models.ForeignKey(
                blank=True,
                help_text="所属的批量检测会话，单张检测时为空",
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to="detection.batchdetectionsession",
                verbose_name="批量检测会话",
            ),
        ),
        migrations.AddIndex(
            model_name="detectionrecord",
            index=models.Index(
                fields=["user", "-upload_time"], name="detection_d_user_id_58a071_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="detectionrecord",
            index=models.Index(
                fields=["batch_session", "batch_index"],
                name="detection_d_batch_s_ab7be8_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="detectionrecord",
            index=models.Index(fields=["status"], name="detection_d_status_1e50ee_idx"),
        ),
        migrations.AddIndex(
            model_name="detectionrecord",
            index=models.Index(
                fields=["is_batch_detection"], name="detection_d_is_batc_e7e182_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="batchdetectionsession",
            index=models.Index(
                fields=["user", "-created_time"], name="detection_b_user_id_197076_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="batchdetectionsession",
            index=models.Index(fields=["status"], name="detection_b_status_98eb14_idx"),
        ),
    ]
