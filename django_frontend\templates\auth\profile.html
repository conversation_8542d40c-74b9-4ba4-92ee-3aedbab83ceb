{% extends 'base.html' %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- 页面标题 -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2>
                <i class="fas fa-user-circle"></i> {{ page_title }}
            </h2>
            <div>
                <a href="{% url 'index' %}" class="btn btn-outline-primary">
                    <i class="fas fa-home"></i> 返回首页
                </a>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 用户信息卡片 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>用户名：</strong>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge bg-primary">{{ user.username }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>邮箱：</strong>
                    </div>
                    <div class="col-sm-8">
                        {% if user.email %}
                            {{ user.email }}
                        {% else %}
                            <span class="text-muted">未设置</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>注册时间：</strong>
                    </div>
                    <div class="col-sm-8">
                        {{ user.date_joined|date:"Y-m-d H:i" }}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>最后登录：</strong>
                    </div>
                    <div class="col-sm-8">
                        {% if user.last_login %}
                            {{ user.last_login|date:"Y-m-d H:i" }}
                        {% else %}
                            <span class="text-muted">从未登录</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>账户状态：</strong>
                    </div>
                    <div class="col-sm-8">
                        {% if user.is_active %}
                            <span class="badge bg-success">正常</span>
                        {% else %}
                            <span class="badge bg-danger">已禁用</span>
                        {% endif %}
                        
                        {% if user.is_superuser %}
                            <span class="badge bg-warning">管理员</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 统计信息卡片 -->
    <div class="col-md-6 mb-4">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>使用统计
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>登录次数：</strong>
                    </div>
                    <div class="col-sm-8">
                        <span class="badge bg-info">{{ profile.login_count }}</span>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>最后登录IP：</strong>
                    </div>
                    <div class="col-sm-8">
                        {% if profile.last_login_ip %}
                            <code>{{ profile.last_login_ip }}</code>
                        {% else %}
                            <span class="text-muted">未记录</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-sm-4">
                        <strong>账户锁定：</strong>
                    </div>
                    <div class="col-sm-8">
                        {% if profile.is_locked %}
                            <span class="badge bg-danger">已锁定</span>
                            {% if profile.locked_until %}
                                <br><small class="text-muted">锁定到：{{ profile.locked_until|date:"Y-m-d H:i" }}</small>
                            {% endif %}
                        {% else %}
                            <span class="badge bg-success">正常</span>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近登录记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>最近登录记录
                </h5>
            </div>
            <div class="card-body">
                {% if recent_logins %}
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>登录时间</th>
                                    <th>IP地址</th>
                                    <th>用户代理</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for login in recent_logins %}
                                <tr>
                                    <td>{{ login.attempt_time|date:"Y-m-d H:i:s" }}</td>
                                    <td><code>{{ login.ip_address }}</code></td>
                                    <td>
                                        <small class="text-muted">
                                            {% if login.user_agent %}
                                                {{ login.user_agent|truncatechars:50 }}
                                            {% else %}
                                                未知
                                            {% endif %}
                                        </small>
                                    </td>
                                    <td>
                                        {% if login.success %}
                                            <span class="badge bg-success">成功</span>
                                        {% else %}
                                            <span class="badge bg-danger">失败</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center text-muted py-4">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <p>暂无登录记录</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- 安全提示 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="alert alert-info">
            <h6><i class="fas fa-shield-alt me-2"></i>安全提示</h6>
            <ul class="mb-0">
                <li>请定期检查您的登录记录，如发现异常请及时联系管理员</li>
                <li>建议使用强密码，包含大小写字母、数字和特殊字符</li>
                <li>不要在公共设备上保存登录状态</li>
                <li>如果长时间不使用，请及时退出登录</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}
